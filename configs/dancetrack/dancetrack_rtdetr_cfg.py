from detectron2.config import LazyCall as L
from omegaconf import OmegaConf
from datasets.builder import build_test_loader
from models.model_utils import build_model
from track import TrackingResult

# 构建数据加载器
dataloader = OmegaConf.create()
dataloader.test = L(build_test_loader)(
    test_size = (800, 1440),
    infer_batch = 1  # 帧对帧跟踪处理
)
 
# 构建模型
model = L(build_model)(
    model_type = 'rtdetr',  # 使用RT-DETR模型
    num_classes = 1,
    confthre = 0.01,
    nmsthre = 0.7,
    
    # RT-DETR特定参数
    backbone = 'resnet50',
    num_queries = 300,
    num_decoder_layers = 6,
    num_feature_levels = 3,
    feat_channels = [256, 512, 1024],
    feat_strides = [8, 16, 32],
    position_embed_type = 'sine',
    dim_feedforward = 1024,
    dropout = 0.1,
    activation = 'relu',
    nhead = 8,
    hidden_dim = 256,
    num_levels = 3,
    num_decoder_points = 4,
    
    # 预训练模型路径
    pretrained_path = 'data/sparsetrack/pretrain/rtdetr_r50vd_2x_coco_objects365_from_paddle.pth'  # 更新为正确的路径
)

# 构建训练配置
train = dict(
    output_dir="outputs/rtdetr_dancetrack",
    init_checkpoint="data/sparsetrack/pretrain/rtdetr_r50vd_2x_coco_objects365_from_paddle.pth",  # 更新为正确的路径
    # model ema
    model_ema = dict(
        enabled=False,
        use_ema_weights_for_eval_only = False,
        decay = 0.9998,
        device = "cuda",
        after_backward = False
    ),
    device="cuda",
)

# 构建跟踪器配置
track = dict(
    experiment_name = "rtdetr_dancetrack_track",
    tracker_type = "ByteTracker",  # 使用ByteTracker
    track_thresh = 0.4,  # 跟踪阈值
    track_buffer = 30,   # 跟踪缓冲区大小
    match_thresh = 0.8,  # 匹配阈值
    min_box_area = 100,  # 最小框面积
    mot20 = False,       # 不使用MOT20
    dance = True,        # 使用DanceTrack数据集
    public_det = False,  # 不使用公共检测结果
    is_public = False,   # 是否使用公共检测结果
    fp16 = False,        # 不使用半精度浮点数
    fuse = False,        # 不融合模型
    down_scale = 1,      # 下采样比例
    layers = 1,          # 层数
    val_ann = 'val_half.json',
    tracking_result_type = TrackingResult.DETECTION,  # 使用检测结果进行跟踪
) 