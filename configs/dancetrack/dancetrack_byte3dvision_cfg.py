from detectron2.config import <PERSON>zyCall as L
from omegaconf import OmegaConf
from datasets.builder import build_test_loader
from models.model_utils import get_model

# build dataloader
dataloader = OmegaConf.create()
dataloader.test = L(build_test_loader)(
    test_size = (800, 1440),  
    infer_batch = 1 # for tracking process frame by frame 
)
 
# build model
model = L(get_model)(
    model_type = 'yolox',
    depth = 1.33,
    width = 1.25,
    num_classes = 1,
    confthre = 0.01,
    nmsthre = 0.7
)

# build train cfg 
train = dict(
    output_dir="./yolox_dance_sparse",
    init_checkpoint="./data/sparsetrack/pretrain/bytetrack_dance.pth.tar",
    # model ema
    model_ema = dict(
        enabled=False,
        use_ema_weights_for_eval_only = False,
        decay = 0.9998,
        device = "cuda",
        after_backward = False
    ),
    device="cuda",
)

# build tracker
track = dict(
    experiment_name = "Byte3DVision",
    track_thresh = 0.6,
    track_buffer = 60,
    match_thresh = 0.9,
    min_box_area = 100,
    down_scale = 4,
    depth_levels = 1,
    depth_levels_low = 12,
    confirm_thresh = 0.7,
    mot20 = False,
    dance = True,
    byte=False,
    deep=False,
    deepm=False,
    byte_vision=False,
    sparse_vision=False,
    byte_deepvision=False,
    byte3D=True,
    pkf=False,
    bot=False,
    sort=False,
    ocsort=False,
    fp16 = True,
    fuse = True,
    # val json
    val_ann="val.json",
    # is public dets using
    is_public=False
)

# For dancetrack--unenable GMC: 368 - 373 in sparse_tracker.py 
# Change the thresh 0.3 to 0.35 during low-score matching 
 
 
 
 





