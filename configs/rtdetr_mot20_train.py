from detectron2.config import LazyCall as L
from omegaconf import OmegaConf
from utils.get_optimizer import get_optimizer
from datasets.builder import build_train_loader, build_test_loader, build_evaluator
from models.model_utils import get_model

# 构建数据加载器
dataloader = OmegaConf.create()
dataloader.train = L(build_train_loader)(
    batch_size = 8,  # 根据GPU内存调整
    num_workers = 4,
    is_distributed = True,
    no_aug = False,
    data_dir = './data/MOT20',  # 使用MOT20数据集路径
    json_file = "train.json",   # 使用转换好的COCO格式训练集标注
    input_size = (896, 896),    # RT-DETR通常使用正方形输入
    degrees = 10.0,
    translate = 0.1,
    scale = (0.1, 2),
    shear = 2.0,
    perspective = 0.0,
    enable_mixup = True,
)
dataloader.test = L(build_test_loader)(
    test_size = (896, 896),
    infer_batch = 4
)
dataloader.evaluator = L(build_evaluator)(output_folder = None)
 
# 构建模型 - 使用RT-DETR
model = L(get_model)(
    model_type = 'rtdetr',  # 使用RT-DETR模型
    num_classes = 1,        # MOT20只有行人一个类别
    confthre = 0.001,
    nmsthre = 0.7,
    
    # RT-DETR特定参数
    rtdetr_model_type = 'rtdetrv2_r50vd',  # 使用R50骨干网络的RT-DETRv2
    backbone = 'resnet50vd', 
    num_queries = 300,      # 查询数量
    hidden_dim = 256,       # 隐藏层维度
    num_levels = 3,
    num_points = 4,
    feat_channels = [256, 256, 256],
    feat_strides = [8, 16, 32],
    dim_feedforward = 1024,
    dropout = 0.0,
    activation = 'relu',
    nhead = 8,
    num_decoder_layers = 6,
    num_encoder_layers = 1,
    use_encoder_idx = [2],
    cross_attn_method = 'default',
    query_select_method = 'default',
    
    # 使用预训练模型
    use_pretrained = True,
    pretrained_path = './checkpoints/rtdetrv2_r50vd_6x_coco_ema.pth',  # 预训练模型路径
)
 
# 构建优化器
optimizer = L(get_optimizer)(
    batch_size = 8,
    basic_lr_per_img = 0.0001 / 64.0,  # 降低学习率进行微调
    model = None, 
    momentum = 0.9, 
    weight_decay = 5e-4,
    warmup_epochs = 1,
    warmup_lr_start = 0
)

# 学习率配置 - 适用于微调
lr_cfg = dict(
    train_batch_size = 8,
    basic_lr_per_img = 0.0001 / 64.0,  # 对于预训练模型微调，使用较小的学习率
    scheduler_name = "yoloxwarmcos",
    iters_per_epoch = 800,   # MOT20数据集较小，根据数据集大小调整
    max_eps = 30,           # 对于微调，不需要太多epoch
    num_warmup_eps = 1,
    warmup_lr_start = 0,
    no_aug_eps = 5,
    min_lr_ratio = 0.05
)

# 训练配置
train = dict(
    output_dir="outputs/rtdetr_mot20",
    init_checkpoint="checkpoints/rtdetrv2_r50vd_6x_coco_ema.pth",  # 预训练权重
    max_iter = lr_cfg['iters_per_epoch'] * lr_cfg['max_eps'],
    start_iter = 0,
    seed = 0,
    random_size = (18, 32), 
    amp=dict(enabled=True),  # 使用混合精度训练加速
    ddp=dict(
        broadcast_buffers=False,
        find_unused_parameters=False,
    ),
    # 模型EMA
    model_ema = dict(
        enabled=True,
        use_ema_weights_for_eval_only = True,
        decay = 0.9998,
        device = "cuda",
        after_backward = False
    ),
    checkpointer=dict(period=lr_cfg['iters_per_epoch'] * 2, max_to_keep=5),
    eval_period = lr_cfg['iters_per_epoch'] * 2,
    log_period = 20,
    device="cuda"
)

# 添加自定义数据集标志
track = dict(
    mot17 = False,
    dance = False,
    your_dataset = False,
    # 使用MOT20
    mot20 = True,
) 