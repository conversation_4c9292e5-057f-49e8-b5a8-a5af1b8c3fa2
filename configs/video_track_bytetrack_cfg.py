"""
ByteTrack视频跟踪配置文件
用于video_tracker_visualizer.py程序

作者: AI Assistant
创建时间: 2025-06-29
"""

from detectron2.config import LazyCall as L
from omegaconf import OmegaConf
from datasets.builder import build_test_loader
from models.model_utils import get_model

# 构建数据加载器
dataloader = OmegaConf.create()
dataloader.test = L(build_test_loader)(
    test_size = (800, 1440),  # 测试图像尺寸
    infer_batch = 1  # 批次大小，视频跟踪逐帧处理
)

# 构建模型
model = L(get_model)(
    model_type = 'yolox',  # 使用YOLOX检测器
    depth = 1.33,          # 模型深度
    width = 1.25,          # 模型宽度
    num_classes = 1,       # 类别数（人）
    confthre = 0.7,        # 置信度阈值（进一步提高以减少误检）
    nmsthre = 0.7          # NMS阈值
)

# 构建训练配置
train = dict(
    output_dir="./video_tracking_output",  # 输出目录
    init_checkpoint="./data/sparsetrack/pretrain/bytetrack_dance.pth.tar",  # 预训练权重
    # 模型EMA配置
    model_ema = dict(
        enabled=False,
        use_ema_weights_for_eval_only = False,
        decay = 0.9998,
        device = "cuda",
        after_backward = False
    ),
    device="cuda",  # 使用GPU
)

# 构建跟踪器配置
track = dict(
    experiment_name = "VideoByteTrack",  # 实验名称
    tracker_type = "ByteTracker",        # 明确指定跟踪器类型

    # 跟踪参数
    track_thresh = 0.6,      # 跟踪阈值
    track_buffer = 30,       # 跟踪缓冲区大小
    match_thresh = 0.9,      # 匹配阈值
    min_box_area = 100,      # 最小框面积

    # 其他参数
    down_scale = 4,          # 下采样比例
    depth_levels = 1,        # 深度层级
    depth_levels_low = 12,   # 低分检测深度层级
    confirm_thresh = 0.7,    # 确认阈值

    # 数据集标志
    mot20 = False,           # 不使用MOT20数据集
    dance = False,           # 不使用DanceTrack数据集

    # 跟踪器类型标志（用于兼容性）
    byte = True,             # 使用ByteTracker
    deep = False,            # 不使用深度特征
    deepm = False,           # 不使用深度匹配
    byte_vision = False,     # 不使用ByteVision
    sparse_vision = False,   # 不使用SparseVision
    byte3D = False,          # 不使用Byte3D
    pkf = False,             # 不使用概率卡尔曼滤波
    bot = False,             # 不使用BoT-SORT
    sort = False,            # 不使用SORT
    ocsort = False,          # 不使用OC-SORT

    # 模型设置
    fp16 = True,             # 使用半精度
    fuse = True,             # 融合模型

    # 验证设置
    val_ann = "val.json",    # 验证注释文件
    is_public = False,       # 不使用公共检测结果
)

# 可视化配置
visualization = dict(
    show_confidence = True,   # 显示置信度
    font_scale = 0.6,        # 字体大小
    thickness = 2,           # 线条粗细
    save_frames = False,     # 是否保存单帧图像
    frame_interval = 1,      # 保存帧间隔
)
