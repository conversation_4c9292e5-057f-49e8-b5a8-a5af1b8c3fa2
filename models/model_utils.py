import os
import sys
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as T
import torchvision
import numpy as np
from pathlib import Path
from detectron2.structures import Instances, Boxes
from PIL import Image, ImageDraw
from typing import List, Dict, Tuple, Optional, Union

logger = logging.getLogger(__name__)

def get_model(model_type='yolox', depth=1.33, width=1.25, num_classes=1, confthre=0.3, nmsthre=0.7, **kwargs):
    """
    根据指定类型获取检测模型
    
    参数:
        model_type: 模型类型，'yolox'或'rtdetr'
        depth: YOLOX深度因子
        width: YOLOX宽度因子
        num_classes: 检测类别数量
        confthre: 置信度阈值
        nmsthre: NMS阈值
        **kwargs: 其他参数
    
    返回:
        nn.Module: 构建的模型
        
    异常:
        ValueError: 如果模型类型不支持
        ImportError: 如果无法导入所需模块
    """
    if model_type == 'yolox':
        try:
            # 导入本地模块
            from .yolo_pafpn import YOLOPAFPN
            from .yolo_head import YOLOXHead
            from .yolox import YOLOX
        except ImportError as e:
            raise ImportError(f"导入YOLOX模块失败: {e}")
    
        def init_yolo(M):
            for m in M.modules():
                if isinstance(m, nn.BatchNorm2d):
                    m.eps = 1e-3
                    m.momentum = 0.03
                elif isinstance(m, nn.Linear):
                    nn.init.normal_(m.weight, mean=0.0, std=0.01)
    
        in_channels = [256, 512, 1024]
        backbone = YOLOPAFPN(depth, width, in_channels=in_channels)
        head = YOLOXHead(num_classes, width, in_channels=in_channels)
        model = YOLOX(backbone, head, confthre, nmsthre)
        
        # 初始化权重
        init_yolo(model)
        
        # 加载预训练权重
        if "ckpt" in kwargs and kwargs["ckpt"] is not None:
            ckpt_file = kwargs["ckpt"]
            logger.info(f"加载YOLOX预训练权重: {ckpt_file}")
            
            try:
                ckpt = torch.load(ckpt_file, map_location="cpu")
                
                # 加载模型权重
                if "model" in ckpt:
                    model.load_state_dict(ckpt["model"])
                else:
                    model.load_state_dict(ckpt)
            except Exception as e:
                logger.error(f"加载检查点失败: {e}")
                raise
        
        return model
    else:
        # 不支持的模型类型
        raise ValueError(f"不支持的模型类型: {model_type}，目前仅支持'yolox'和'rtdetr'")

build_model = get_model