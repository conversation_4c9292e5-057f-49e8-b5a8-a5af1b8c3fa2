#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用于检查MOT17数据集路径的工具脚本
"""

import os
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_mot17_paths(data_dir='./data/MOT17'):
    """检查MOT17数据集路径"""
    data_dir = os.path.abspath(data_dir)
    
    # 检查目录是否存在
    if not os.path.exists(data_dir):
        logger.error(f"MOT17数据集目录不存在: {data_dir}")
        return False
    
    # 检查训练和验证集标注文件
    train_ann = os.path.join(data_dir, 'annotations/train.json')
    val_ann = os.path.join(data_dir, 'annotations/val_half.json')
    
    status = True
    
    if not os.path.exists(train_ann):
        logger.error(f"训练集标注文件不存在: {train_ann}")
        status = False
    else:
        logger.info(f"训练集标注文件存在: {train_ann}")
    
    if not os.path.exists(val_ann):
        logger.warning(f"验证集标注文件不存在: {val_ann}")
        logger.warning("请确保验证集标注文件已正确创建")
    else:
        logger.info(f"验证集标注文件存在: {val_ann}")
    
    # 检查图像目录
    train_img_dir = os.path.join(data_dir, 'train')
    
    if not os.path.exists(train_img_dir):
        logger.error(f"训练集图像目录不存在: {train_img_dir}")
        status = False
    else:
        logger.info(f"训练集图像目录存在: {train_img_dir}")
    
    return status

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="检查MOT17数据集路径")
    parser.add_argument('--data-dir', type=str, default='./data/MOT17', help='MOT17数据集路径')
    args = parser.parse_args()
    
    logger.info(f"检查MOT17数据集路径: {args.data_dir}")
    if check_mot17_paths(args.data_dir):
        logger.info("MOT17数据集路径检查通过!")
    else:
        logger.error("MOT17数据集路径检查失败!")

if __name__ == "__main__":
    main() 