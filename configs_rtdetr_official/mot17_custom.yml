task: detection

evaluator:
  type: CocoEvaluator
  iou_types: ['bbox', ]

# MOT17只有一个类别：行人
num_classes: 1
remap_mscoco_category: False

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: /home/<USER>/PythonProjects/MultiFeatureTrack/data/MOT17/train/
    ann_file: /home/<USER>/PythonProjects/MultiFeatureTrack/data/MOT17/annotations/train.json
    return_masks: False
    transforms:
      type: Compose
      ops: 
        - type: Resize
          size: [896, 896]
        - type: ConvertPILImage
          dtype: float32
          scale: True
      policy:
        epoch: 27  # 在最后3个epoch停止数据增强
  shuffle: True
  num_workers: 4
  drop_last: True 
  collate_fn:
    type: BatchImageCollateFuncion
    scales: [768, 800, 832, 864, 896]  # 多尺度训练
    stop_epoch: 27  # 在最后3个epoch停止多尺度

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: /home/<USER>/PythonProjects/MultiFeatureTrack/data/MOT17/train/
    ann_file: /home/<USER>/PythonProjects/MultiFeatureTrack/data/MOT17/annotations/val_half.json
    return_masks: False
    transforms:
      type: Compose
      ops: 
        - type: Resize
          size: [896, 896]
        - type: ConvertPILImage
          dtype: float32
          scale: True
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn:
    type: BatchImageCollateFuncion
