__include__: [
  'configs_rtdetr_official/mot17_custom.yml',
  'RT_DETR/rtdetrv2_pytorch/configs/runtime.yml',
  'RT_DETR/rtdetrv2_pytorch/configs/rtdetrv2/include/dataloader.yml',
  'RT_DETR/rtdetrv2_pytorch/configs/rtdetrv2/include/optimizer.yml',
  'RT_DETR/rtdetrv2_pytorch/configs/rtdetrv2/include/rtdetrv2_r50vd.yml',
]

output_dir: outputs/rtdetr_mot17_official
tuning: data/sparsetrack/pretrain/rtdetrv2_r50vd_m_7x_coco_ema.pth

HybridEncoder:
  expansion: 0.5

RTDETRTransformerv2:
  eval_idx: 2  # 使用第3个解码器层进行评估
  cross_attn_method: discrete
  num_queries: 300  # 查询数量

RTDETRPostProcessor:
  num_top_queries: 300  # 确保与num_queries一致
  score_threshold: 0.001  # 置信度阈值

# 模型评估尺寸
eval_spatial_size: [896, 896]

# 训练轮数
epoches: 30

# 优化器配置
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*(?:score_head|class_embed)).*$'
      lr: 0.001  # 为分类头设置更高的学习率，加速适应单类别任务
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001  # 为骨干网络设置较低的学习率
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 数据加载器配置
train_dataloader: 
  total_batch_size: 8
