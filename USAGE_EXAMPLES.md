# 视频跟踪可视化使用示例

本文档提供了详细的使用示例，帮助您快速上手视频跟踪可视化功能。

## 前置条件

1. **确保环境配置正确**
   ```bash
   # 检查Python环境
   python --version  # 应该是3.7+
   
   # 检查关键依赖
   python -c "import torch; print(torch.__version__)"
   python -c "import cv2; print(cv2.__version__)"
   python -c "import detectron2; print('detectron2 installed')"
   ```

2. **确保模型权重文件存在**
   ```bash
   # 检查预训练权重
   ls -la data/sparsetrack/pretrain/bytetrack_dance.pth.tar
   
   # 检查FastReID权重（用于ByteVisionTrack）
   ls -la fast_reid/pretrained/mot17_sbs_S50.pth
   ```

## 快速开始示例

### 示例1：使用演示脚本（推荐新手）

```bash
# 创建测试视频（如果没有现成的视频）
python test_video_tracker.py  # 这会验证环境并创建测试视频

# 使用ByteTracker跟踪
python demo_video_tracking.py --input your_video.mp4 --tracker bytetrack

# 使用ByteVisionTracker跟踪
python demo_video_tracking.py --input your_video.mp4 --tracker bytevision

# 同时比较两种跟踪器
python demo_video_tracking.py --input your_video.mp4 --tracker both
```

### 示例2：使用主程序进行精细控制

```bash
# ByteTracker跟踪，显示实时画面并保存视频
python video_tracker_visualizer.py \
    --input sample_video.mp4 \
    --tracker bytetrack \
    --output bytetrack_result.mp4

# ByteVisionTracker跟踪，只保存视频不显示
python video_tracker_visualizer.py \
    --input sample_video.mp4 \
    --tracker bytevision \
    --output bytevision_result.mp4 \
    --no-display

# 使用自定义配置文件
python video_tracker_visualizer.py \
    --input sample_video.mp4 \
    --config configs/video_track_bytetrack_cfg.py \
    --weights path/to/your/weights.pth \
    --output custom_result.mp4
```

## 详细使用场景

### 场景1：处理监控视频

```bash
# 对于监控视频，通常使用ByteTracker即可获得良好效果
python video_tracker_visualizer.py \
    --input surveillance_video.mp4 \
    --tracker bytetrack \
    --output surveillance_tracked.mp4
```

**预期效果**：
- 实时显示跟踪过程
- 每个人员有唯一的ID和颜色
- 生成带跟踪信息的输出视频

### 场景2：处理拥挤场景视频

```bash
# 对于人员密集、遮挡较多的场景，推荐使用ByteVisionTracker
python video_tracker_visualizer.py \
    --input crowded_scene.mp4 \
    --tracker bytevision \
    --output crowded_tracked.mp4
```

**预期效果**：
- 更好的遮挡处理能力
- 相似外观目标的区分能力更强
- 处理速度稍慢但精度更高

### 场景3：批量处理多个视频

创建批处理脚本 `batch_process.py`：

```python
import os
import subprocess

video_dir = "input_videos"
output_dir = "output_videos"
tracker = "bytetrack"  # 或 "bytevision"

os.makedirs(output_dir, exist_ok=True)

for video_file in os.listdir(video_dir):
    if video_file.endswith(('.mp4', '.avi', '.mov')):
        input_path = os.path.join(video_dir, video_file)
        output_path = os.path.join(output_dir, f"{tracker}_{video_file}")
        
        cmd = [
            "python", "video_tracker_visualizer.py",
            "--input", input_path,
            "--tracker", tracker,
            "--output", output_path,
            "--no-display"  # 批处理时不显示画面
        ]
        
        print(f"处理: {video_file}")
        subprocess.run(cmd)
        print(f"完成: {output_path}")
```

运行批处理：
```bash
python batch_process.py
```

### 场景4：性能测试和比较

```bash
# 创建性能测试脚本
python demo_video_tracking.py \
    --input test_video.mp4 \
    --tracker both \
    --output performance_test

# 这将生成两个输出文件：
# - performance_test/test_video_bytetrack.mp4
# - performance_test/test_video_bytevision.mp4
# 以及详细的性能统计信息
```

## 常见问题解决

### 问题1：CUDA内存不足

```bash
# 解决方案1：使用CPU模式
export CUDA_VISIBLE_DEVICES=""
python video_tracker_visualizer.py --input video.mp4 --tracker bytetrack

# 解决方案2：减小输入视频分辨率
ffmpeg -i input.mp4 -vf scale=640:480 input_small.mp4
python video_tracker_visualizer.py --input input_small.mp4 --tracker bytetrack
```

### 问题2：模型权重文件不存在

```bash
# 检查权重文件路径
ls -la data/sparsetrack/pretrain/
ls -la fast_reid/pretrained/

# 如果文件不存在，请从项目说明中下载相应的权重文件
```

### 问题3：视频格式不支持

```bash
# 转换视频格式
ffmpeg -i input.avi -c:v libx264 -c:a aac input.mp4
python video_tracker_visualizer.py --input input.mp4 --tracker bytetrack
```

### 问题4：跟踪效果不理想

```bash
# 尝试调整跟踪参数（需要修改配置文件）
# 或者尝试不同的跟踪器

# 对于快速移动的目标
python video_tracker_visualizer.py --input video.mp4 --tracker bytetrack

# 对于遮挡严重的场景
python video_tracker_visualizer.py --input video.mp4 --tracker bytevision
```

## 输出文件说明

### 视频输出
- **格式**：MP4 (H.264编码)
- **内容**：原视频 + 彩色跟踪框 + ID标签 + 置信度分数
- **命名**：`{原文件名}_{跟踪器类型}_tracked.mp4`

### 控制台输出
```
视频信息: 1920x1080, 30.00fps, 900帧
处理进度: 30/900 (3.3%)
处理进度: 60/900 (6.7%)
...
视频处理完成，共处理 900 帧
跟踪统计信息:
  总帧数: 900
  总跟踪数: 15420
  平均每帧跟踪数: 17.13
  唯一ID数量: 25
  使用的跟踪器: ByteTracker
```

## 高级用法

### 自定义可视化效果

修改 `utils/tracker_utils.py` 中的参数：

```python
# 修改颜色生成
def generate_colors(num_colors: int) -> List[Tuple[int, int, int]]:
    # 自定义颜色方案
    pass

# 修改绘制样式
def draw_tracking_results(...):
    # 自定义框的粗细、字体大小等
    pass
```

### 添加新的跟踪器

1. 在 `utils/tracker_utils.py` 中添加跟踪器类型判断
2. 在 `video_tracker_visualizer.py` 中添加初始化逻辑
3. 创建对应的配置文件

### 集成到其他项目

```python
from video_tracker_visualizer import VideoTrackerVisualizer

# 创建可视化器
visualizer = VideoTrackerVisualizer("configs/video_track_bytetrack_cfg.py")

# 处理视频
tracks = visualizer.process_video("input.mp4", "output.mp4")

# 获取跟踪结果进行后续处理
for frame_tracks in tracks:
    # 处理每帧的跟踪结果
    pass
```

## 性能优化建议

1. **GPU使用**：确保CUDA可用以获得最佳性能
2. **视频分辨率**：较大的视频需要更多处理时间
3. **跟踪器选择**：
   - ByteTracker：速度优先，适合实时应用
   - ByteVisionTracker：精度优先，适合离线处理
4. **批处理**：使用 `--no-display` 选项可以提高批处理速度

## 技术支持

如果遇到问题，请：

1. 首先运行测试脚本：`python test_video_tracker.py`
2. 检查日志输出中的错误信息
3. 确认所有依赖项都已正确安装
4. 查看 `VIDEO_TRACKING_README.md` 获取更多技术细节
