# RT-DETR官方训练实现指南

本文档指导您如何使用RT-DETR官方实现进行MOT17数据集上的训练，避免detectron2框架加载EMA权重时的兼容性问题。

## 1. 环境准备

确保您已安装RT-DETR所需的依赖：

```bash
pip install -r RT_DETR/rtdetrv2_pytorch/requirements.txt
```

## 2. 数据准备

确保MOT17数据集已转换为COCO格式，目录结构如下：

```
data/
  MOT17/
    annotations/
      train.json       # 训练集标注
      val_half.json    # 验证集标注
    train/             # 训练集图像
    test/              # 测试集图像（可选）
```

## 3. 训练模型

### 3.1 基本训练命令

使用以下命令在MOT17数据集上训练RT-DETR模型：

```bash
# 使用默认预训练权重路径(data/sparsetrack/pretrain/rtdetrv2_r50vd_m_7x_coco_ema.pth)
python train_rtdetr_official.py --num-gpus 1

# 如果预训练权重不存在，可以添加下载参数
python train_rtdetr_official.py --num-gpus 1 --download-weights

# 多GPU训练
python train_rtdetr_official.py --num-gpus 4
```

### 3.2 自定义参数

您可以使用以下参数自定义训练过程：

- `--num-gpus`: 使用的GPU数量（默认：1）
- `--data-dir`: MOT17数据集路径（默认：./data/MOT17）
- `--batch-size`: 训练批次大小（默认：8）
- `--max-epochs`: 最大训练轮数（默认：30）
- `--download-weights`: 自动下载预训练权重（当指定的预训练权重不存在时）
- `--output-dir`: 输出目录（默认：outputs/rtdetr_mot17_official）
- `--resume`: 从断点恢复训练
- `--input-size`: 输入图像尺寸，格式为height,width（默认：896,896）
- `--pretrained-weights`: 预训练权重路径（默认：data/sparsetrack/pretrain/rtdetrv2_r50vd_m_7x_coco_ema.pth）

例如：

```bash
# 使用自定义参数
python train_rtdetr_official.py --num-gpus 2 --batch-size 16 --max-epochs 50 --input-size 640,640 --pretrained-weights /path/to/your/weights.pth
```

### 3.3 恢复训练

如果训练中断，可以使用以下命令恢复训练：

```bash
python train_rtdetr_official.py --num-gpus 1 --resume
```

## 4. 模型评估

训练过程中会定期评估模型性能，评估结果会保存在输出目录中。

## 5. 故障排除

### 5.1 数据集路径问题

如果遇到数据集路径问题，可以使用以下命令检查MOT17数据集是否正确配置：

```bash
python configs_rtdetr_official/dataset_paths.py --data-dir ./data/MOT17
```

### 5.2 内存不足

如果遇到内存不足问题，可以尝试：

- 减小批处理大小（--batch-size）
- 减小输入图像尺寸（--input-size）
- 使用单GPU训练（--num-gpus 1）

### 5.3 训练不稳定

如果训练过程不稳定，修改训练脚本中的学习率或优化器参数。

## 6. 与Detectron2实现的区别

官方RT-DETR实现与Detectron2实现的主要区别：

1. **权重兼容性**：官方实现直接支持EMA权重格式
2. **训练流程**：官方实现使用原生PyTorch训练流程，更贴近论文实现
3. **配置方式**：官方实现使用YAML配置文件，而Detectron2使用Python配置
4. **数据增强**：官方实现有特定的数据增强策略
5. **性能表现**：官方实现可能有更好的性能表现，因为它针对RT-DETR进行了优化

## 7. 注意事项

- 确保数据集已正确转换为COCO格式
- 检查GPU内存是否足够支持所选的批处理大小和输入尺寸
- 训练时间会根据GPU性能和数据集大小而变化
- 官方实现可能会随着RT-DETR模型的更新而变化，建议关注官方仓库的更新 