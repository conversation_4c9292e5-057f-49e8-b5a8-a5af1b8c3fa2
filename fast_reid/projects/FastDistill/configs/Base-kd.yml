_BASE_: ../../../configs/Base-SBS.yml

MODEL:
  BACKBONE:
    NAME: build_resnet_backbone_distill
    WITH_IBN: False
    WITH_NL: False
    PRETRAIN: True

INPUT:
  SIZE_TRAIN: [ 256, 128 ]
  SIZE_TEST: [ 256, 128 ]

SOLVER:
  MAX_EPOCH: 60
  BASE_LR: 0.0007
  IMS_PER_BATCH: 256

  DELAY_EPOCHS: 30
  FREEZE_ITERS: 500

  CHECKPOINT_PERIOD: 20

TEST:
  EVAL_PERIOD: 20
  IMS_PER_BATCH: 128

CUDNN_BENCHMARK: True
