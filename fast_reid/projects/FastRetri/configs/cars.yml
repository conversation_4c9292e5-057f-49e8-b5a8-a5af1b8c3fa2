_BASE_: base-image_retri.yml

MODEL:
  LOSSES:
    CE:
      EPSILON: 0.4

INPUT:
  CJ:
    ENABLED: True
    BRIGHTNESS: 0.3
    CONTRAST: 0.3
    SATURATION: 0.3
    HUE: 0.1

  CROP:
    RATIO: (1., 1.)

SOLVER:
  MAX_EPOCH: 100

  BASE_LR: 0.05
  ETA_MIN_LR: 0.0005

  NESTEROV: False
  MOMENTUM: 0.

TEST:
  RECALLS: [ 1, 2, 4, 8, 16, 32 ]

DATASETS:
  NAMES: ("Cars196",)
  TESTS: ("Cars196",)

OUTPUT_DIR: projects/FastRetri/logs/r50-base_cars
