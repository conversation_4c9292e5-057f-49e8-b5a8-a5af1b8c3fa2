name: lint_python
on: [pull_request, push]
jobs:
  lint_python:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
      - run: pip install black codespell flake8 isort pytest
      - run: black --check . || true
      - run: codespell --quiet-level=2 || true  # --ignore-words-list="" --skip=""
      - run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      - run: isort --profile black . || true
      - run: pip install -r requirements.txt || true
      - run: pytest . || true
