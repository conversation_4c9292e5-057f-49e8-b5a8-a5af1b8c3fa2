import os.path as osp
import os
import numpy as np
from tqdm import tqdm

def mkdir_if_missing(d):
    if osp.isfile(d):
        d = osp.dirname(d)
    if not osp.exists(d):
        os.makedirs(d)

data_root = osp.join('data/gt/mot_challenge') # the root directory of the dataset
gt_folder = osp.join(data_root, 'MOT20-train')
seqs_str = '''MOT20-01
              MOT20-02
              MOT20-03
              MOT20-05'''
seqs = [seq.strip() for seq in seqs_str.split()]

def gen_gt_val():
    for seq in tqdm(seqs):
        print('start seq {}'.format(seq))
        seq_info = open(osp.join(gt_folder, seq, 'seqinfo.ini')).read()
        seqLength = int(seq_info[seq_info.find('seqLength=') + 10:seq_info.find('\nimWidth')])
        gt_txt = osp.join(gt_folder, seq, 'gt', 'gt.txt')
        gt = np.loadtxt(gt_txt, dtype=np.float64, delimiter=',')
        save_val_gt = osp.join(gt_folder, seq, 'gt', 'gt_val_half.txt')
        val_start = seqLength // 2 + 1
        with open(save_val_gt, 'w') as f:
            for obj in gt:
                if obj[0] > val_start:
                    new_frame = obj[0] - val_start  # 重新计算帧号，从1开始
                    label_str = '{:d},{:d},{:d},{:d},{:d},{:d},{:d},{:d},{:.6f}\n'.format(
                        int(new_frame), int(obj[1]), int(obj[2]), int(obj[3]), int(obj[4]),
                        int(obj[5]), int(obj[6]), int(obj[7]), obj[8])
                    f.write(label_str)

if __name__ == '__main__':
    gen_gt_val()