import numpy as np

# 输入文件路径
input_file = 'data/gt/mot_challenge/MOT17-train/MOT17-04-FRCNN/gt/gt.txt'
# 输出文件路径
output_file = 'data/gt/mot_challenge/MOT17-train/MOT17-04-FRCNN/gt//filtered_gt.txt'

# 读取原始数据
data = np.loadtxt(input_file, dtype=np.float64, delimiter=',')

# 过滤行人数据，假设行人ID为1
pedestrian_data = data[data[:, 1] == 1]

# 将过滤后的数据保存到新的文件中
np.savetxt(output_file, pedestrian_data, fmt='%d,%d,%d,%d,%d,%d,%d,%d,%.5f', delimiter=',')

print("过滤完成，结果已保存到", output_file)
