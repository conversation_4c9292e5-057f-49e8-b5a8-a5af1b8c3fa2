import logging
import os
import glob
import motmetrics as mm
from pathlib import Path
from collections import OrderedDict
import torch
import torch.backends.cudnn as cudnn
import random
import numpy as np

from detectron2.utils import comm
from detectron2.config import CfgNode
from detectron2.utils.file_io import PathManager
from detectron2.utils.logger import setup_logger
from detectron2.config import LazyConfig, instantiate
from detectron2.engine.defaults import create_ddp_model, _try_get_key, _highlight
from detectron2.utils.collect_env import collect_env_info
from detectron2.checkpoint import DetectionCheckpointer
from detectron2.engine import (
    launch,
    default_argument_parser,
    default_setup
)

from utils import ema 
from models.model_utils import build_model
from tracker.eval.evaluators import MOTEvaluator
from register_data import register_dataset

# 定义追踪结果类型枚举值
class TrackingResult:
    DETECTION = "detection"
    PUBLIC_DETECTION = "public_detection"

logger = logging.getLogger("detectron2")

def compare_dataframes(gts, ts):
    accs = []
    names = []
    for k, tsacc in ts.items():
        if k in gts:            
            logger.info('Comparing {}...'.format(k))
            accs.append(mm.utils.compare_to_groundtruth(gts[k], tsacc, 'iou', distth=0.5))
            names.append(k)
        else:
            logger.warning('No ground truth for {}, skipping.'.format(k))

    return accs, names


def fuse_model(model):
    """融合模型的卷积和批处理规范层以加速评估"""
    logger.info("尝试融合模型层...")
    if hasattr(model, 'fuse'):
        model.fuse()
    else:
        logger.warning("模型没有fuse方法，跳过融合")
    return model


def do_track(cfg, model):
    logger = logging.getLogger("detectron2")
    if cfg.train.model_ema.enabled and cfg.train.model_ema.use_ema_weights_for_eval_only:
        logger.info("运行评估 (使用EMA权重)")
    else:
        logger.info("运行评估 (不使用EMA权重)")
        
    cudnn.benchmark = True
        
    # 设置分布式推理的环境变量    
    file_name = os.path.join(cfg.train.output_dir, cfg.track.experiment_name)
    if comm.is_main_process():
        os.makedirs(file_name, exist_ok=True)

    results_folder = os.path.join(file_name, "track_results")    
    os.makedirs(results_folder, exist_ok=True)    

    # 构建评估器
    evaluator = MOTEvaluator(
        args=cfg,
        dataloader=instantiate(cfg.dataloader.test),
    )

    model.eval()
    if cfg.track.fuse:
        logger.info("\t融合模型...")
        model = fuse_model(model)

    # 开始评估
    evaluator.evaluate(
        model,  cfg.track.fp16, results_folder
    )

    # 评估MOTA指标
    mm.lap.default_solver = 'lap'

    if cfg.track.val_ann == 'val_half.json':
        gt_type = '_val_half'
    else:
        gt_type = ''
    print('gt_type', gt_type)

    if cfg.track.mot20:
        gtfiles = glob.glob(os.path.join('data/MOT20/train', '*/gt/gt{}.txt'.format(gt_type)))
        print("正在评估MOT20数据集")
        print("gt_files", gtfiles)
    elif cfg.track.dance:
        gtfiles = glob.glob(os.path.join('data/dancetrack/val', '*/gt/gt{}.txt'.format(gt_type)))
        print("正在评估DanceTrack数据集")
        print("gt_files", gtfiles)
    else:
        gtfiles = glob.glob(os.path.join('data/MOT17/train', '*/gt/gt{}.txt'.format(gt_type)))
        print("正在评估MOT17数据集")
        print('gt_files', gtfiles)

    tsfiles = [f for f in glob.glob(os.path.join(results_folder, '*.txt')) if not os.path.basename(f).startswith('eval')]
    print(tsfiles)

    logger.info('找到 {} 个真值文件和 {} 个测试文件.'.format(len(gtfiles), len(tsfiles)))
    logger.info('可用的LAP求解器 {}'.format(mm.lap.available_solvers))
    logger.info('默认LAP求解器 \'{}\''.format(mm.lap.default_solver))
    logger.info('正在加载文件.')
    
    gt = OrderedDict([(Path(f).parts[-3], mm.io.loadtxt(f, fmt='mot15-2D', min_confidence=1)) for f in gtfiles])
    ts = OrderedDict([(os.path.splitext(Path(f).parts[-1])[0], mm.io.loadtxt(f, fmt='mot15-2D', min_confidence=-1)) for f in tsfiles])    
    
    mh = mm.metrics.create()    
    accs, names = compare_dataframes(gt, ts)
    
    logger.info('运行评估指标')
    metrics = ['recall', 'precision', 'num_unique_objects', 'mostly_tracked',
               'partially_tracked', 'mostly_lost', 'num_false_positives', 'num_misses',
               'num_switches', 'num_fragmentations', 'mota', 'motp', 'num_objects']
    summary = mh.compute_many(accs, names=names, metrics=metrics, generate_overall=True)

    logger.info('\n'+mm.io.render_summary(summary, formatters=mh.formatters, namemap=mm.io.motchallenge_metric_names))
    logger.info('评估完成')

def main(args):
    cfg = LazyConfig.load(args.config_file)
    cfg = LazyConfig.apply_overrides(cfg, args.opts)
    default_setup(cfg, args)
    
    # 添加可视化和调试选项
    if args.visualize:
        cfg.track.visualize = True
        print("已启用检测结果可视化")
        
    if args.debug:
        cfg.track.debug = True
        print("已启用调试模式")
    
    register_dataset(cfg)  # 注册数据集
    
    # 根据配置构建模型（直接从cfg.model传递所有参数）
    kwargs = {}
    if isinstance(cfg.model, dict):
        kwargs = {k: v for k, v in cfg.model.items()}
    else:
        # 对于LazyCall格式的配置，需要获取所有参数
        for k in dir(cfg.model):
            if not k.startswith('_'):
                kwargs[k] = getattr(cfg.model, k)
                
    model = build_model(**kwargs)
    
    # 检查CUDA是否可用
    if cfg.train.device.startswith('cuda') and not torch.cuda.is_available():
        logger.warning("CUDA不可用，切换到CPU设备")
        cfg.train.device = 'cpu'
    
    model.to(cfg.train.device)
    model.device = torch.device(cfg.train.device)
    model = create_ddp_model(model)
    
    # 使用EMA进行评估
    ema.may_build_model_ema(cfg, model)
    
    # 处理检查点加载
    if hasattr(cfg.train, 'init_checkpoint') and cfg.train.init_checkpoint:
        checkpoint_file = cfg.train.init_checkpoint
        logger.info(f"从 {checkpoint_file} 加载检查点")
        
        # 直接加载检查点并处理
        checkpoint = torch.load(checkpoint_file, map_location=torch.device('cpu'))
        
        # 如果是字典格式，提取模型权重
        if isinstance(checkpoint, dict):
            if 'model' in checkpoint:
                model_state_dict = checkpoint['model']
            else:
                model_state_dict = checkpoint
                
            # 移除所有非模型权重的键
            model_state_dict = {k: v for k, v in model_state_dict.items() 
                              if isinstance(v, (torch.Tensor, np.ndarray))}
            
            # 检查模型类型
            model_type = kwargs.get('model_type', 'rtdetr')
            
            if model_type == 'rtdetr':
                # 检查预训练模型是否没有"detr_model."前缀
                has_detr_prefix = any(k.startswith('detr_model.') for k in model_state_dict.keys())
                
                if not has_detr_prefix:
                    # 添加detr_model前缀
                    logger.info("添加'detr_model.'前缀到RT-DETR预训练权重")
                    new_state_dict = {}
                    for k, v in model_state_dict.items():
                        new_state_dict[f'detr_model.{k}'] = v
                    model_state_dict = new_state_dict
            
            # 使用strict=False来允许部分加载
            logger.info("使用非严格模式加载RT-DETR模型权重")
            model.load_state_dict(model_state_dict, strict=False)
            logger.info("成功加载RT-DETR模型权重")
        else:
            # 如果不是字典格式，直接加载
            model.load_state_dict(checkpoint, strict=False)
            logger.info("成功加载模型权重（直接格式）")
    
    # 为评估应用EMA状态
    if cfg.train.model_ema.enabled and cfg.train.model_ema.use_ema_weights_for_eval_only:
        ema.apply_model_ema(model)
    
    do_track(cfg, model)

if __name__ == "__main__":
    # 只创建一次参数解析器
    parser = default_argument_parser(epilog = "RT-DETR多目标跟踪评估")
    # 添加自定义参数
    parser.add_argument("--visualize", action="store_true", help="启用检测结果可视化")
    parser.add_argument("--debug", action="store_true", help="启用详细调试信息")
    # 解析所有参数
    args = parser.parse_args()

    launch(
        main,
        args.num_gpus,
        num_machines=args.num_machines,
        machine_rank=args.machine_rank,
        dist_url=args.dist_url,
        args=(args,),
    )
'''
CUDA_VISIBLE_DEVICES=0 python3 track.py  --num-gpus 1  --config-file configs/mot17/mot17_rtdetr_track_cfg.py 

CUDA_VISIBLE_DEVICES=0 python3 track.py  --num-gpus 1  --config-file configs/mot20/mot20_rtdetr_track_cfg.py 

CUDA_VISIBLE_DEVICES=0 python3 track.py  --num-gpus 1  --config-file configs/dancetrack/dancetrack_rtdetr_cfg.py 
'''
