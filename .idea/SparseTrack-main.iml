<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/TrackEval" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/utils" isTestSource="false" />
    </content>
    <orderEntry type="jdk" jdkName="bytetrack" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="EPYTEXT" />
    <option name="myDocStringFormat" value="Epytext" />
  </component>
  <component name="TestRunnerService">
    <option name="PROJECT_TEST_RUNNER" value="py.test" />
  </component>
</module>