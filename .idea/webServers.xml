<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WebServers">
    <option name="servers">
      <webServer id="de27eaaa-60b6-49bc-bbcc-b9b6d69c6b52" name="sun-computer">
        <fileTransfer rootFolder="/home/<USER>" accessType="SFTP" host="***************" port="22" sshConfigId="109f0f55-0847-4bdb-955c-4275e8259ac1" sshConfig="sun@***************:22 password">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" keepAliveTimeout="0" passiveMode="true" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="32abbf42-92b4-492c-9cd6-d367a003ebdf" name="sun-office">
        <fileTransfer rootFolder="/home/<USER>" accessType="SFTP" host="***************" port="22" sshConfigId="109f0f55-0847-4bdb-955c-4275e8259ac1" sshConfig="sun@***************:22 password">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" keepAliveTimeout="0" passiveMode="true" shareSSLContext="true" isUseSudo="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="9720f7ad-4202-48c1-9de1-49cd356edb87" name="sun-remote">
        <fileTransfer rootFolder="/root" accessType="SFTP" host="connect.cqa1.seetacloud.com" port="51881" sshConfigId="0417cf13-9e1d-4087-b374-351a481b811f" sshConfig="<EMAIL>:51881 password">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" passiveMode="true" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
    </option>
  </component>
</project>