<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="com.obiscr.chatgpt.settings.EasyCodeState">
    <option name="projectFiles" value="$PROJECT_DIR$/datasets/data/datasets/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/datasets/datasets_wrapper.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/datasets/mosaicdetection.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/datasets/mot.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/data_augment.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/data_prefetcher.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/dataloading.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/data/samplers.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/builder.py;/home/<USER>/PycharmProjects/SparseTrack-main/datasets/mot_mapper.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/darknet.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/losses.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/model_utils.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/network_blocks.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/yolo_fpn.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/yolo_head.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/yolo_pafpn.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/yolov7_backbone.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/yolov7_fpn.py;/home/<USER>/PycharmProjects/SparseTrack-main/models/yolox.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/convert_cityperson_to_coco.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/convert_crowdhuman_to_coco.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/convert_dance_to_coco.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/convert_ethz_to_coco.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/convert_mot17_to_coco.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/convert_mot20_to_coco.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/mix_data_ablation.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/mix_data_ablation_20.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/mix_data_test_mot17.py;/home/<USER>/PycharmProjects/SparseTrack-main/tools/mix_data_test_mot20.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/eval/evaluators.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/eval/timer.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/basetrack.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/bot_sort.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/byte_tracker.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/byte_tracker_levels.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/kalman_filter.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/matching.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/oc_sort.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/sort.py;/home/<USER>/PycharmProjects/SparseTrack-main/tracker/sparse_tracker.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/docs/How_To/Add_a_new_metric.md;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/docs/MOTChallenge-Official/Readme.md;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/docs/RobMOTS-Official/Readme.md;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/comparison_plots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_bdd.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_davis.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_headtracking_challenge.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_kitti.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_kitti_mots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_mot_challenge.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_mots_challenge.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_rob_mots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_tao.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/scripts/run_youtube_vis.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/tests/test_all_quick.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/tests/test_davis.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/tests/test_metrics.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/tests/test_mot17.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/tests/test_mots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/baseline_utils.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/non_overlap.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/pascal_colormap.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/stp.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/thresholder.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/baselines/vizualize.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/_base_dataset.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/bdd100k.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/davis.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/head_tracking_challenge.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/kitti_2d_box.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/kitti_mots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/mot_challenge_2d_box.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/mots_challenge.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/rob_mots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/rob_mots_classmap.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/run_rob_mots.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/tao.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/datasets/youtube_vis.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/_base_metric.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/clear.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/count.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/hota.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/identity.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/ideucl.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/j_and_f.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/track_map.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/metrics/vace.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/__init__.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/_timing.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/eval.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/plotting.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/trackeval/utils.py;/home/<USER>/PycharmProjects/SparseTrack-main/TrackEval/Readme.md;/home/<USER>/PycharmProjects/SparseTrack-main/utils/boxes.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/dist.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/ema.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/get_optimizer.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/lr_scheduler.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/model_utils.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/mosaic_close.py;/home/<USER>/PycharmProjects/SparseTrack-main/utils/multiscale.py;/home/<USER>/PycharmProjects/SparseTrack-main/dancetrack_bot_cfg.py;/home/<USER>/PycharmProjects/SparseTrack-main/dancetrack_sparse_cfg.py;/home/<USER>/PycharmProjects/SparseTrack-main/interpolation.py;/home/<USER>/PycharmProjects/SparseTrack-main/levels_ablation_20.py;/home/<USER>/PycharmProjects/SparseTrack-main/mot17_ab_track_cfg.py;/home/<USER>/PycharmProjects/SparseTrack-main/mot17_track_cfg.py;/home/<USER>/PycharmProjects/SparseTrack-main/mot17_train_config.py;/home/<USER>/PycharmProjects/SparseTrack-main/mot20_ab_track_cfg.py;/home/<USER>/PycharmProjects/SparseTrack-main/mot20_track_cfg.py;/home/<USER>/PycharmProjects/SparseTrack-main/mot20_train_config.py;/home/<USER>/PycharmProjects/SparseTrack-main/python_module.cpp;/home/<USER>/PycharmProjects/SparseTrack-main/README.md;/home/<USER>/PycharmProjects/SparseTrack-main/register_data.py;/home/<USER>/PycharmProjects/SparseTrack-main/track.py;/home/<USER>/PycharmProjects/SparseTrack-main/train.py" />
  </component>
</project>