<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="<EMAIL>:51881 password" remoteFilesAllowedToDisappearOnAutoupload="false" autoUploadExternalChanges="true">
    <serverData>
      <paths name="<EMAIL>:51881 password">
        <serverdata>
          <mappings>
            <mapping deploy="/sparsetrack" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:51881 password (2)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/pycharm_project_167" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="sun-computer">
        <serverdata>
          <mappings>
            <mapping deploy="/PycharmProjects/SparseTrack-main" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="sun-office">
        <serverdata>
          <mappings>
            <mapping deploy="/PycharmProjects/SparseTrack-main" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="sun-remote">
        <serverdata>
          <mappings>
            <mapping deploy="/sparsetrack" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>