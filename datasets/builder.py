from detectron2.data import (
    build_detection_test_loader,
)
from detectron2.evaluation import (
    COCOEvaluator,
)
from detectron2.config import get_cfg
import torch.distributed as dist
import os, torch
import logging


# test data cfg
opt = get_cfg()
opt.DATASETS.TEST = ("my_val",)
opt.OUTPUT_DIR = "./yolox_mix17"
opt.DATALOADER.NUM_WORKERS = 4
opt.TEST.DETECTIONS_PER_IMAGE = 600

    
def build_train_loader(
    data_dir, json_file, input_size, degrees, translate, scale, shear, perspective, enable_mixup, num_workers, batch_size, is_distributed, no_aug=False
):
    from datasets.data import (
        MOTDataset,
        TrainTransform,
        YoloBatchSampler,
        DataLoader,
        InfiniteSampler,
        MosaicDetection,
    )
    logger = logging.getLogger("detectron2")
    
    logger.info(f"构建训练数据加载器 - 参数:")
    logger.info(f"  数据目录: {data_dir}")
    logger.info(f"  标注文件: {json_file}")
    logger.info(f"  输入尺寸: {input_size}")
    logger.info(f"  批量大小: {batch_size}")
    logger.info(f"  启用Mosaic: {not no_aug}")
    logger.info(f"  启用MixUp: {enable_mixup}")

    dataset = MOTDataset(
        data_dir= data_dir,
        json_file=json_file,
        name='train',
        img_size= input_size,
        preproc=TrainTransform(
            rgb_means=(0.485, 0.456, 0.406),
            std=(0.229, 0.224, 0.225),
            max_labels=600,
        ),
    )

    dataset = MosaicDetection(
        dataset,
        mosaic=not no_aug,
        img_size= input_size,
        preproc=TrainTransform(
            rgb_means=(0.485, 0.456, 0.406),
            std=(0.229, 0.224, 0.225),
            max_labels=1200,
        ),
        degrees= degrees,
        translate= translate,
        scale= scale,
        shear= shear,
        perspective= perspective,
        enable_mixup= enable_mixup,
    )

    if is_distributed:
        batch_size = batch_size // dist.get_world_size()
        logger.info(f"  分布式训练: 每个GPU的批量大小调整为 {batch_size}")

    sampler = InfiniteSampler(
        len(dataset), seed=0
    )

    batch_sampler = YoloBatchSampler(
        sampler=sampler,
        batch_size=batch_size,
        drop_last=False,
        input_dimension= input_size,
        mosaic=not no_aug,
    )

    dataloader_kwargs = {"num_workers": num_workers , "pin_memory": True}
    dataloader_kwargs["batch_sampler"] = batch_sampler
    train_loader = DataLoader(dataset, **dataloader_kwargs)
    
    # 创建样本数据检查
    logger.info("正在检查数据加载器的输出格式...")
    sample_iter = iter(train_loader)
    try:
        first_batch = next(sample_iter)
        if isinstance(first_batch, (list, tuple)):
            logger.info(f"数据加载器返回类型: {type(first_batch)}, 长度: {len(first_batch)}")
            if len(first_batch) > 0:
                first_item = first_batch[0]
                logger.info(f"第一个元素类型: {type(first_item)}")
                if isinstance(first_item, torch.Tensor):
                    logger.info(f"第一个元素形状: {first_item.shape}")
                if len(first_batch) > 1:
                    second_item = first_batch[1]
                    logger.info(f"第二个元素类型: {type(second_item)}")
                    if isinstance(second_item, (list, tuple)):
                        logger.info(f"第二个元素长度: {len(second_item)}")
        else:
            logger.info(f"数据加载器返回类型: {type(first_batch)}")
    except Exception as e:
        logger.warning(f"检查数据加载器输出时出错: {e}")

    return train_loader

def build_test_loader(test_size, infer_batch = 1):
    """
    Returns:
        iterable

    It now calls :func:`detectron2.data.build_detection_test_loader`.
    Overwrite it if you'd like a different data loader.
    """
    from datasets.mot_mapper import MOTtestMapper
    from datasets.data import ValTransform
   
    print(" building val loader ...")
    mapper = MOTtestMapper(
        test_size = test_size,
        preproc = ValTransform(
            rgb_means=(0.485, 0.456, 0.406),
            std=(0.229, 0.224, 0.225),
        ),
    )
    return build_detection_test_loader(opt, dataset_name = opt.DATASETS.TEST[0], mapper=mapper, batch_size = infer_batch)

def build_evaluator(output_folder=None):
    """
    Create evaluator(s) for a given dataset.
    This uses the special metadata "evaluator_type" associated with each builtin dataset.
    For your own dataset, you can simply create an evaluator manually in your
    script and do not have to worry about the hacky if-else logic here.
    """
    print(" building val evaluator ...")
    if output_folder is None:
        output_folder = os.path.join(opt.OUTPUT_DIR, "inference")
    return COCOEvaluator(opt.DATASETS.TEST[0], output_dir=output_folder)
