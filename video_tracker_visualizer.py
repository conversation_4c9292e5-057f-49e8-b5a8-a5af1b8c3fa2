"""
视频跟踪可视化程序
调用ByteTrack和ByteVisionTrack对输入视频进行跟踪并可视化

作者: AI Assistant
创建时间: 2025-06-29
使用方法:
    python video_tracker_visualizer.py --input video.mp4 --tracker bytetrack --output output.mp4
    python video_tracker_visualizer.py --input video.mp4 --tracker bytevision --output output.mp4
"""

import os
import sys
import cv2
import torch
import numpy as np
import argparse
import logging
from pathlib import Path
from typing import List, Optional, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from detectron2.config import LazyConfig
from detectron2.utils.logger import setup_logger
from detectron2.structures import Instances, Boxes

# 导入跟踪器
from tracker.byte_tracker import BYTETracker
from tracker.bytevision_tracker import BYTEVisionTracker
from tracker.sparse_tracker import SparseTracker

# 导入模型相关
from models.model_utils import get_model

# 导入工具函数
from utils.tracker_utils import (
    determine_tracker_type,
    draw_tracking_results,
    save_tracking_video,
    print_tracking_stats
)

logger = logging.getLogger("video_tracker")


class VideoTrackerVisualizer:
    """视频跟踪可视化器"""

    def __init__(self, config_path: str, model_weights: str = None):
        """
        初始化视频跟踪可视化器

        Args:
            config_path: 配置文件路径
            model_weights: 模型权重路径（可选）
        """
        self.config_path = config_path
        self.model_weights = model_weights

        # 加载配置
        self.cfg = LazyConfig.load(config_path)

        # 确定跟踪器类型
        self.tracker_type = determine_tracker_type(self.cfg.track)
        logger.info(f"使用跟踪器类型: {self.tracker_type}")

        # 初始化模型
        self._init_model()

        # 初始化跟踪器
        self._init_tracker()

    def _init_model(self):
        """初始化检测模型"""
        logger.info("初始化检测模型...")

        # 构建模型
        model_kwargs = {}
        if isinstance(self.cfg.model, dict):
            model_kwargs = {k: v for k, v in self.cfg.model.items()}
        else:
            # 对于LazyCall格式的配置
            for k in dir(self.cfg.model):
                if not k.startswith('_'):
                    model_kwargs[k] = getattr(self.cfg.model, k)

        self.model = get_model(**model_kwargs)

        # 设置设备
        device = getattr(self.cfg.train, 'device', 'cuda' if torch.cuda.is_available() else 'cpu')
        self.device = torch.device(device)
        self.model.to(self.device)
        self.model.eval()

        # 加载权重
        if self.model_weights:
            checkpoint_path = self.model_weights
        elif hasattr(self.cfg.train, 'init_checkpoint'):
            checkpoint_path = self.cfg.train.init_checkpoint
        else:
            raise ValueError("未指定模型权重路径")

        if checkpoint_path and os.path.exists(checkpoint_path):
            logger.info(f"加载模型权重: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location=self.device)

            # 处理不同格式的checkpoint
            if isinstance(checkpoint, dict):
                if 'model' in checkpoint:
                    state_dict = checkpoint['model']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint

            # 加载权重
            self.model.load_state_dict(state_dict, strict=False)
            logger.info("模型权重加载完成")
        else:
            logger.warning(f"模型权重文件不存在: {checkpoint_path}")

    def _init_tracker(self):
        """初始化跟踪器"""
        logger.info(f"初始化跟踪器: {self.tracker_type}")

        if self.tracker_type == "ByteTracker":
            self.tracker = BYTETracker(self.cfg.track)
        elif self.tracker_type == "BYTEVisionTracker":
            self.tracker = BYTEVisionTracker(self.cfg.track)
        elif self.tracker_type == "SparseTracker":
            self.tracker = SparseTracker(self.cfg.track)
        else:
            raise ValueError(f"不支持的跟踪器类型: {self.tracker_type}")

    def preprocess_frame(self, frame: np.ndarray) -> Tuple[torch.Tensor, float]:
        """
        预处理视频帧

        Args:
            frame: 输入帧 (BGR格式)

        Returns:
            Tuple[torch.Tensor, float]: (预处理后的张量, 缩放比例)
        """
        # 转换为RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # 获取目标尺寸
        if hasattr(self.cfg.dataloader, 'test') and hasattr(self.cfg.dataloader.test, 'test_size'):
            target_size = self.cfg.dataloader.test.test_size
        else:
            target_size = (800, 1440)  # 默认尺寸

        # 调整尺寸
        h, w = frame_rgb.shape[:2]
        target_h, target_w = target_size

        # 计算缩放比例
        scale = min(target_h / h, target_w / w)
        new_h, new_w = int(h * scale), int(w * scale)

        # 调整尺寸
        resized = cv2.resize(frame_rgb, (new_w, new_h))

        # 填充到目标尺寸
        padded = np.zeros((target_h, target_w, 3), dtype=np.uint8)
        padded[:new_h, :new_w] = resized

        # 转换为张量 - 确保正确的维度顺序
        # 从 (H, W, C) 转换为 (C, H, W) - 不添加batch维度！
        tensor = torch.from_numpy(padded).permute(2, 0, 1).float()

        # 调试信息
        logger.debug(f"预处理张量形状: {tensor.shape}")
        logger.debug(f"预处理张量数据类型: {tensor.dtype}")

        # 移动到设备
        tensor = tensor.to(self.device)

        return tensor, scale

    def detect_frame(self, frame: np.ndarray) -> Instances:
        """
        对单帧进行目标检测

        Args:
            frame: 输入帧

        Returns:
            Instances: 检测结果
        """
        # 预处理
        tensor, scale = self.preprocess_frame(frame)

        # 调试：打印张量形状
        logger.debug(f"预处理后张量形状: {tensor.shape}")

        # 构建输入数据 - tensor应该是3维的 [C, H, W]
        batch_data = [{
            "image": tensor,
            "ori_img": frame,
            "height": frame.shape[0],
            "width": frame.shape[1]
        }]

        # 推理
        with torch.no_grad():
            outputs = self.model(batch_data)

        # 获取检测结果
        instances = outputs[0]["instances"]

        # 调整检测框坐标到原图尺寸
        if len(instances) > 0:
            boxes = instances.pred_boxes.tensor
            boxes = boxes / scale  # 缩放回原图尺寸
            instances.pred_boxes = Boxes(boxes)

        return instances

    def process_video(
        self,
        input_path: str,
        output_path: str = None,
        display: bool = True,
        save_video: bool = True
    ) -> List[List]:
        """
        处理视频文件

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            display: 是否实时显示
            save_video: 是否保存视频

        Returns:
            List[List]: 每帧的跟踪结果
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"输入视频文件不存在: {input_path}")

        # 打开视频
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {input_path}")

        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        logger.info(f"视频信息: {width}x{height}, {fps:.2f}fps, {total_frames}帧")

        # 准备输出视频写入器
        if save_video and output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        # 重置跟踪器
        self.tracker.frame_id = 0
        if hasattr(self.tracker, 'tracked_stracks'):
            self.tracker.tracked_stracks = []
            self.tracker.lost_stracks = []
            self.tracker.removed_stracks = []

        tracks_per_frame = []
        frame_idx = 0

        logger.info("开始处理视频...")

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_idx += 1

            # 检测
            instances = self.detect_frame(frame)

            # 跟踪
            if len(instances) > 0:
                online_targets = self.tracker.update(instances, frame)
            else:
                online_targets = []

            # 保存跟踪结果
            tracks_per_frame.append(online_targets)

            # 可视化
            vis_frame = draw_tracking_results(frame, online_targets, self.tracker_type)

            # 添加信息文本
            info_text = f"Frame: {frame_idx}/{total_frames}, Tracks: {len(online_targets)}, Tracker: {self.tracker_type}"
            cv2.putText(vis_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 显示
            if display:
                cv2.imshow('Video Tracking', vis_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break

            # 保存
            if save_video and output_path:
                out.write(vis_frame)

            # 进度信息
            if frame_idx % 30 == 0:
                logger.info(f"处理进度: {frame_idx}/{total_frames} ({frame_idx/total_frames*100:.1f}%)")

        # 清理资源
        cap.release()
        if save_video and output_path:
            out.release()
        if display:
            cv2.destroyAllWindows()

        logger.info(f"视频处理完成，共处理 {frame_idx} 帧")

        # 打印统计信息
        print_tracking_stats(tracks_per_frame, self.tracker_type)

        return tracks_per_frame


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="视频跟踪可视化程序")
    parser.add_argument("--input", "-i", required=True, help="输入视频路径")
    parser.add_argument("--output", "-o", help="输出视频路径")
    parser.add_argument("--tracker", "-t", choices=["bytetrack", "bytevision"],
                       default="bytetrack", help="跟踪器类型")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--weights", "-w", help="模型权重路径")
    parser.add_argument("--no-display", action="store_true", help="不显示实时画面")
    parser.add_argument("--no-save", action="store_true", help="不保存输出视频")

    args = parser.parse_args()

    # 设置日志
    setup_logger(name="video_tracker")

    # 确定配置文件
    if args.config:
        config_path = args.config
    else:
        if args.tracker == "bytetrack":
            config_path = "configs/video_track_bytetrack_cfg.py"
        elif args.tracker == "bytevision":
            config_path = "configs/video_track_bytevision_cfg.py"
        else:
            raise ValueError(f"不支持的跟踪器类型: {args.tracker}")

    # 确定输出路径
    if not args.output and not args.no_save:
        input_path = Path(args.input)
        args.output = str(input_path.parent / f"{input_path.stem}_{args.tracker}_tracked{input_path.suffix}")

    logger.info(f"输入视频: {args.input}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"配置文件: {config_path}")

    try:
        # 创建可视化器
        visualizer = VideoTrackerVisualizer(config_path, args.weights)

        # 处理视频
        tracks = visualizer.process_video(
            args.input,
            args.output,
            display=not args.no_display,
            save_video=not args.no_save
        )

        logger.info("视频跟踪可视化完成！")

    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
