"""
测试修复后的视频跟踪器
验证输出质量和跟踪效果

作者: AI Assistant
创建时间: 2025-06-30
"""

import cv2
import os
import sys

def test_output_video(video_path: str):
    """测试输出视频的质量"""
    if not os.path.exists(video_path):
        print(f"❌ 输出视频不存在: {video_path}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ 无法打开输出视频: {video_path}")
        return False
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"✅ 输出视频信息:")
    print(f"   - 分辨率: {width}x{height}")
    print(f"   - 帧率: {fps:.2f}fps")
    print(f"   - 总帧数: {total_frames}")
    
    # 检查几个关键帧
    test_frames = [0, total_frames//4, total_frames//2, total_frames*3//4, total_frames-1]
    
    for frame_idx in test_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if not ret:
            print(f"❌ 无法读取第{frame_idx}帧")
            continue
        
        # 检查帧是否有内容
        if frame is None or frame.size == 0:
            print(f"❌ 第{frame_idx}帧为空")
            continue
        
        # 检查是否有跟踪框（简单的颜色检测）
        # 跟踪框通常是彩色的，检查是否有非灰度像素
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        color_diff = cv2.absdiff(frame[:,:,0], gray) + cv2.absdiff(frame[:,:,1], gray) + cv2.absdiff(frame[:,:,2], gray)
        has_color = cv2.countNonZero(color_diff) > 1000  # 有足够的彩色像素
        
        print(f"   - 第{frame_idx}帧: {'✅ 有彩色内容' if has_color else '⚠️  主要是灰度'}")
    
    cap.release()
    return True


def compare_videos(original_path: str, tracked_path: str):
    """比较原始视频和跟踪后的视频"""
    print("\n=== 视频比较 ===")
    
    if not os.path.exists(original_path):
        print(f"❌ 原始视频不存在: {original_path}")
        return
    
    if not os.path.exists(tracked_path):
        print(f"❌ 跟踪视频不存在: {tracked_path}")
        return
    
    # 打开两个视频
    cap_orig = cv2.VideoCapture(original_path)
    cap_track = cv2.VideoCapture(tracked_path)
    
    if not cap_orig.isOpened() or not cap_track.isOpened():
        print("❌ 无法打开视频文件")
        return
    
    # 比较基本信息
    orig_frames = int(cap_orig.get(cv2.CAP_PROP_FRAME_COUNT))
    track_frames = int(cap_track.get(cv2.CAP_PROP_FRAME_COUNT))
    
    orig_fps = cap_orig.get(cv2.CAP_PROP_FPS)
    track_fps = cap_track.get(cv2.CAP_PROP_FPS)
    
    print(f"原始视频: {orig_frames}帧, {orig_fps:.2f}fps")
    print(f"跟踪视频: {track_frames}帧, {track_fps:.2f}fps")
    
    if orig_frames == track_frames:
        print("✅ 帧数匹配")
    else:
        print("⚠️  帧数不匹配")
    
    # 显示几帧进行对比
    print("\n按任意键查看帧对比，按'q'退出...")
    
    frame_indices = [0, orig_frames//4, orig_frames//2, orig_frames*3//4]
    
    for frame_idx in frame_indices:
        # 读取对应帧
        cap_orig.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        cap_track.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        
        ret_orig, frame_orig = cap_orig.read()
        ret_track, frame_track = cap_track.read()
        
        if not ret_orig or not ret_track:
            continue
        
        # 调整大小以便显示
        height = 400
        width_orig = int(frame_orig.shape[1] * height / frame_orig.shape[0])
        width_track = int(frame_track.shape[1] * height / frame_track.shape[0])
        
        frame_orig_resized = cv2.resize(frame_orig, (width_orig, height))
        frame_track_resized = cv2.resize(frame_track, (width_track, height))
        
        # 并排显示
        combined = cv2.hconcat([frame_orig_resized, frame_track_resized])
        
        # 添加标题
        cv2.putText(combined, f"Frame {frame_idx}: Original (Left) vs Tracked (Right)", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('Video Comparison', combined)
        
        key = cv2.waitKey(0) & 0xFF
        if key == ord('q'):
            break
    
    cap_orig.release()
    cap_track.release()
    cv2.destroyAllWindows()


def main():
    """主函数"""
    print("=== 测试修复后的视频跟踪器 ===")
    
    # 测试输出视频
    output_video = "palace_fixed.mp4"
    original_video = "videos/palace.mp4"
    
    print("\n1. 测试输出视频质量...")
    test_output_video(output_video)
    
    print("\n2. 比较原始视频和跟踪视频...")
    compare_videos(original_video, output_video)
    
    print("\n=== 测试完成 ===")
    print("如果看到跟踪框和ID标签，说明修复成功！")


if __name__ == "__main__":
    main()
