"""
调试视频跟踪器
用于诊断张量维度问题

作者: AI Assistant
创建时间: 2025-06-30
"""

import os
import sys
import cv2
import torch
import numpy as np
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from detectron2.config import LazyConfig
from detectron2.utils.logger import setup_logger
from detectron2.structures import Instances, Boxes

# 导入模型相关
from models.model_utils import get_model

# 设置日志
setup_logger(name="debug")
logger = logging.getLogger("debug")

def debug_model_input():
    """调试模型输入格式"""

    # 加载配置
    config_path = "configs/video_track_bytetrack_cfg.py"
    cfg = LazyConfig.load(config_path)

    # 构建模型
    model_kwargs = {}
    if isinstance(cfg.model, dict):
        model_kwargs = {k: v for k, v in cfg.model.items()}
    else:
        for k in dir(cfg.model):
            if not k.startswith('_'):
                model_kwargs[k] = getattr(cfg.model, k)

    model = get_model(**model_kwargs)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()

    # 加载权重
    checkpoint_path = cfg.train.init_checkpoint
    if os.path.exists(checkpoint_path):
        logger.info(f"加载模型权重: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)

        if isinstance(checkpoint, dict):
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
        else:
            state_dict = checkpoint

        model.load_state_dict(state_dict, strict=False)
        logger.info("模型权重加载完成")

    # 创建测试输入
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

    # 预处理
    frame_rgb = cv2.cvtColor(test_frame, cv2.COLOR_BGR2RGB)
    target_size = (800, 1440)

    h, w = frame_rgb.shape[:2]
    target_h, target_w = target_size

    scale = min(target_h / h, target_w / w)
    new_h, new_w = int(h * scale), int(w * scale)

    resized = cv2.resize(frame_rgb, (new_w, new_h))

    padded = np.zeros((target_h, target_w, 3), dtype=np.uint8)
    padded[:new_h, :new_w] = resized

    # 转换为张量 - 不添加batch维度
    tensor = torch.from_numpy(padded).permute(2, 0, 1).float().to(device)

    logger.info(f"输入张量形状: {tensor.shape}")
    logger.info(f"输入张量数据类型: {tensor.dtype}")

    # 构建输入数据
    batch_data = [{
        "image": tensor,
        "ori_img": test_frame,
        "height": test_frame.shape[0],
        "width": test_frame.shape[1]
    }]

    logger.info(f"batch_data[0]['image']形状: {batch_data[0]['image'].shape}")

    # 测试模型推理
    try:
        with torch.no_grad():
            outputs = model(batch_data)
        logger.info("模型推理成功！")
        logger.info(f"输出类型: {type(outputs)}")
        if isinstance(outputs, list) and len(outputs) > 0:
            logger.info(f"输出[0]类型: {type(outputs[0])}")
            if "instances" in outputs[0]:
                instances = outputs[0]["instances"]
                logger.info(f"检测到 {len(instances)} 个目标")
    except Exception as e:
        logger.error(f"模型推理失败: {e}")
        logger.error(f"错误类型: {type(e)}")

        # 打印更详细的错误信息
        import traceback
        logger.error(f"完整错误信息:\n{traceback.format_exc()}")


def debug_video_processing():
    """调试视频处理"""

    video_path = "videos/palace.mp4"

    if not os.path.exists(video_path):
        logger.error(f"视频文件不存在: {video_path}")
        return

    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.error(f"无法打开视频: {video_path}")
        return

    # 读取第一帧
    ret, frame = cap.read()
    if not ret:
        logger.error("无法读取视频帧")
        cap.release()
        return

    logger.info(f"视频帧形状: {frame.shape}")
    logger.info(f"视频帧数据类型: {frame.dtype}")

    # 测试预处理
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    logger.info(f"RGB帧形状: {frame_rgb.shape}")

    cap.release()


def main():
    """主函数"""
    logger.info("开始调试视频跟踪器...")

    logger.info("=== 调试视频处理 ===")
    debug_video_processing()

    logger.info("=== 调试模型输入 ===")
    debug_model_input()

    logger.info("调试完成")


if __name__ == "__main__":
    main()
