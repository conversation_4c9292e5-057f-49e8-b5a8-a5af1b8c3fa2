#!/usr/bin/env python
"""
将MOT17数据集转换为COCO检测数据集格式
"""
import os
import json
import glob
import argparse
import numpy as np
from tqdm import tqdm
from collections import defaultdict

def parse_args():
    parser = argparse.ArgumentParser(description='Convert MOT17 to COCO format')
    parser.add_argument('--input-dir', help='MOT17 数据目录')
    parser.add_argument('--output-dir', help='输出COCO格式注释的目录')
    parser.add_argument('--train-val-split', type=float, default=0.9,
                       help='训练集比例，默认为0.9')
    parser.add_argument('--only-pedestrian', action='store_true',
                       help='只保留行人类别（类别1）')
    return parser.parse_args()

def load_mot_txt(txt_path, only_pedestrian=True):
    """
    加载MOT格式的标注文件
    格式: <frame_id> <track_id> <x> <y> <w> <h> <conf> <class_id> <visibility>
    """
    if not os.path.exists(txt_path):
        print(f"文件不存在: {txt_path}")
        return []
    
    annotations = []
    with open(txt_path, 'r') as f:
        for line in f:
            data = line.strip().split(',')
            frame_id = int(data[0])
            track_id = int(data[1])
            x = float(data[2])
            y = float(data[3])
            w = float(data[4])
            h = float(data[5])
            conf = float(data[6])
            class_id = int(data[7])
            visibility = float(data[8]) if len(data) > 8 else 1.0
            
            # 如果只需要行人（类别1）
            if only_pedestrian and class_id != 1:
                continue
                
            annotation = {
                'frame_id': frame_id,
                'track_id': track_id,
                'bbox': [x, y, w, h],
                'conf': conf,
                'class_id': 0,  # COCO格式从0开始，MOT中行人是1
                'visibility': visibility
            }
            annotations.append(annotation)
    
    return annotations

def create_coco_annotations(sequences, output_dir, train_ratio=0.9):
    """创建COCO格式的注释文件"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化COCO格式数据
    coco_format = {
        'info': {
            'description': 'MOT17 Dataset in COCO format',
            'version': '1.0',
            'year': 2023,
            'contributor': 'Auto Converter',
            'date_created': '2023-01-01'
        },
        'licenses': [],
        'categories': [
            {
                'id': 0,
                'name': 'pedestrian',
                'supercategory': 'person'
            }
        ],
        'images': [],
        'annotations': []
    }
    
    # 随机划分训练集和验证集
    seq_names = list(sequences.keys())
    np.random.shuffle(seq_names)
    train_size = int(len(seq_names) * train_ratio)
    train_seqs = seq_names[:train_size]
    val_seqs = seq_names[train_size:]
    
    train_coco = coco_format.copy()
    train_coco['images'] = []
    train_coco['annotations'] = []
    
    val_coco = coco_format.copy()
    val_coco['images'] = []
    val_coco['annotations'] = []
    
    image_id = 0
    anno_id = 0
    
    # 处理每个序列
    for seq_name, seq_data in tqdm(sequences.items()):
        is_train = seq_name in train_seqs
        target_coco = train_coco if is_train else val_coco
        
        frame_to_annos = defaultdict(list)
        for anno in seq_data['annotations']:
            frame_to_annos[anno['frame_id']].append(anno)
        
        # 获取图像尺寸（假设所有帧都相同）
        width, height = seq_data['size']
        
        # 为每个帧创建图像和注释
        for frame_id, annos in frame_to_annos.items():
            img_filename = f"{seq_name}/img1/{frame_id:06d}.jpg"
            
            # 添加图像信息
            image_info = {
                'id': image_id,
                'file_name': img_filename,
                'width': width,
                'height': height
            }
            target_coco['images'].append(image_info)
            
            # 添加该图像的所有注释
            for anno in annos:
                x, y, w, h = anno['bbox']
                
                # 转换为COCO格式 [x,y,w,h] -> [x,y,w,h]（无需转换，已经是相同格式）
                annotation = {
                    'id': anno_id,
                    'image_id': image_id,
                    'category_id': anno['class_id'],
                    'bbox': [x, y, w, h],
                    'area': w * h,
                    'iscrowd': 0,
                    'track_id': anno['track_id']
                }
                target_coco['annotations'].append(annotation)
                anno_id += 1
            
            image_id += 1
    
    # 保存COCO格式注释文件
    with open(os.path.join(output_dir, 'train.json'), 'w') as f:
        json.dump(train_coco, f)
    
    with open(os.path.join(output_dir, 'val.json'), 'w') as f:
        json.dump(val_coco, f)
    
    print(f"已生成训练集注释：{len(train_coco['images'])}张图像, {len(train_coco['annotations'])}个标注")
    print(f"已生成验证集注释：{len(val_coco['images'])}张图像, {len(val_coco['annotations'])}个标注")

def main():
    args = parse_args()
    
    # 获取所有MOT17序列
    seq_dirs = glob.glob(os.path.join(args.input_dir, 'MOT17-*-*'))
    sequences = {}
    
    # 处理每个序列
    for seq_dir in tqdm(seq_dirs):
        seq_name = os.path.basename(seq_dir)
        
        # 加载groundtruth文件
        gt_file = os.path.join(seq_dir, 'gt', 'gt.txt')
        annotations = load_mot_txt(gt_file, args.only_pedestrian)
        
        if not annotations:
            print(f"警告：序列 {seq_name} 没有有效标注，跳过")
            continue
        
        # 获取序列图像尺寸（从第一张图像）
        img_dir = os.path.join(seq_dir, 'img1')
        img_files = sorted(glob.glob(os.path.join(img_dir, '*.jpg')))
        if not img_files:
            print(f"警告：序列 {seq_name} 没有图像，跳过")
            continue
        
        # 获取图像尺寸
        from PIL import Image
        img = Image.open(img_files[0])
        width, height = img.size
        
        sequences[seq_name] = {
            'annotations': annotations,
            'size': (width, height)
        }
    
    # 创建COCO格式注释
    create_coco_annotations(sequences, args.output_dir, args.train_val_split)

if __name__ == '__main__':
    main() 