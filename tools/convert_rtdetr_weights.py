#!/usr/bin/env python
# -*- encoding: utf-8 -*-

import argparse
import os
import torch
from loguru import logger


def make_parser():
    parser = argparse.ArgumentParser("RT-DETR预训练权重转换工具")
    parser.add_argument(
        "--source", default="checkpoints/rtdetr_r50vd_6x_coco.pth", type=str, help="源权重文件路径"
    )
    parser.add_argument(
        "--target", default="checkpoints/rtdetr_r50vd_6x_coco_converted.pth", type=str, help="目标权重文件路径"
    )
    parser.add_argument("--with_prefix", action="store_true", help="是否添加前缀 'detr_model.'")
    parser.add_argument("--remove_prefix", action="store_true", help="是否移除前缀 'detr_model.'")
    return parser


def convert_rtdetr_weights(src_path, dst_path, with_prefix=False, remove_prefix=False):
    """
    转换RT-DETR预训练权重格式
    
    Args:
        src_path (str): 源权重文件路径
        dst_path (str): 目标权重文件路径
        with_prefix (bool): 是否添加前缀 'detr_model.'
        remove_prefix (bool): 是否移除前缀 'detr_model.'
    """
    if not os.path.exists(src_path):
        logger.error(f"源文件不存在: {src_path}")
        return False
    
    logger.info(f"正在加载权重文件: {src_path}")
    ckpt = torch.load(src_path, map_location="cpu")
    
    # 处理可能的不同格式
    if "model" in ckpt:
        logger.info("从权重文件中提取'model'键")
        state_dict = ckpt["model"]
    elif "state_dict" in ckpt:
        logger.info("从权重文件中提取'state_dict'键")
        state_dict = ckpt["state_dict"]
    else:
        state_dict = ckpt
        logger.info("直接使用权重文件作为状态字典")
    
    # 创建新的状态字典
    new_state_dict = {}
    
    # 处理键名
    for k, v in state_dict.items():
        new_k = k
        
        # 移除前缀
        if remove_prefix and k.startswith("detr_model."):
            new_k = k[len("detr_model."):]
        
        # 添加前缀
        if with_prefix and not new_k.startswith("detr_model."):
            new_k = "detr_model." + new_k
        
        new_state_dict[new_k] = v
        
        if new_k != k:
            logger.info(f"键名转换: {k} -> {new_k}")
    
    # 保存新的权重文件
    os.makedirs(os.path.dirname(dst_path), exist_ok=True)
    
    # 构建保存字典，保持与原始权重文件相同的结构
    save_dict = {}
    if "model" in ckpt:
        save_dict["model"] = new_state_dict
        # 保留其他键
        for k, v in ckpt.items():
            if k != "model":
                save_dict[k] = v
    elif "state_dict" in ckpt:
        save_dict["state_dict"] = new_state_dict
        # 保留其他键
        for k, v in ckpt.items():
            if k != "state_dict":
                save_dict[k] = v
    else:
        save_dict = new_state_dict
    
    torch.save(save_dict, dst_path)
    logger.info(f"转换后的权重已保存至: {dst_path}")
    return True


def main():
    args = make_parser().parse_args()
    
    if args.with_prefix and args.remove_prefix:
        logger.error("不能同时添加和移除前缀")
        return
    
    success = convert_rtdetr_weights(
        args.source, 
        args.target, 
        with_prefix=args.with_prefix, 
        remove_prefix=args.remove_prefix
    )
    
    if success:
        logger.info("权重转换成功")
    else:
        logger.error("权重转换失败")


if __name__ == "__main__":
    main() 