#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RT-DETR检测结果可视化脚本

此脚本可以用来测试RT-DETR检测器的性能，并生成可视化结果。
使用方法:
    python visualize_detections.py --config configs/mot17/mot17_rtdetr_track_cfg.py --image path/to/image.jpg
"""

import argparse
import torch
import cv2
import numpy as np
import os
from detectron2.config import LazyConfig
from models.model_utils import build_model
from detectron2.structures import Instances, Boxes
from PIL import Image
import matplotlib.pyplot as plt

def parse_args():
    parser = argparse.ArgumentParser("RT-DETR检测可视化工具")
    parser.add_argument("--config", type=str, default="configs/mot17/mot17_rtdetr_track_cfg.py", 
                        help="配置文件路径")
    parser.add_argument("--image", type=str, required=True, 
                        help="输入图像路径")
    parser.add_argument("--threshold", type=float, default=0.05, 
                        help="检测阈值")
    parser.add_argument("--output", type=str, default="detection_result.jpg", 
                        help="输出图像路径")
    parser.add_argument("--device", type=str, default="cuda", 
                        help="设备类型 (cuda/cpu)")
    return parser.parse_args()

def visualize_detection(image, outputs, threshold=0.05, output_path="detection_result.jpg"):
    """可视化检测结果"""
    # 获取检测结果
    if len(outputs.pred_boxes) == 0:
        print("未检测到任何目标!")
        return image
    
    # 获取检测框和分数
    boxes = outputs.pred_boxes.tensor.cpu().numpy()
    scores = outputs.scores.cpu().numpy()
    
    # 显示检测数量和分数分布
    print(f"检测到 {len(boxes)} 个目标")
    if len(scores) > 0:
        print(f"分数范围: {scores.min():.4f}-{scores.max():.4f}, 平均: {scores.mean():.4f}")
    
    # 过滤低分检测
    keep = scores > threshold
    boxes = boxes[keep]
    scores = scores[keep]
    
    print(f"阈值 {threshold} 过滤后保留 {len(boxes)} 个目标")
    
    # 转换为PIL图像以便绘制
    image_np = np.array(image)
    
    # 设置不同颜色
    colors = [
        (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
        (0, 255, 255), (255, 0, 255), (128, 0, 0), (0, 128, 0)
    ]
    
    # 绘制检测框
    for i, (box, score) in enumerate(zip(boxes, scores)):
        x1, y1, x2, y2 = map(int, box)
        color = colors[i % len(colors)]
        
        # 绘制边界框
        cv2.rectangle(image_np, (x1, y1), (x2, y2), color, 2)
        
        # 添加文本标签
        text = f"{score:.2f}"
        font_scale = 0.6
        thickness = 2
        text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
        
        # 标签背景
        cv2.rectangle(image_np, (x1, y1 - text_size[1] - 10), (x1 + text_size[0], y1), color, -1)
        # 标签文字
        cv2.putText(image_np, text, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), thickness)
    
    # 保存结果
    cv2.imwrite(output_path, image_np[:, :, ::-1])  # 转换回BGR格式保存
    print(f"可视化结果已保存到: {output_path}")
    
    return image_np

def main():
    args = parse_args()
    
    # 读取配置
    cfg = LazyConfig.load(args.config)
    print("已加载配置文件")
    
    # 构建模型
    kwargs = {}
    if isinstance(cfg.model, dict):
        kwargs = {k: v for k, v in cfg.model.items()}
    else:
        for k in dir(cfg.model):
            if not k.startswith("_"):
                kwargs[k] = getattr(cfg.model, k)
    
    # 设置检测阈值
    kwargs["confthre"] = args.threshold
    
    # 构建模型
    model = build_model(**kwargs)
    model.to(args.device)
    model.eval()
    print("模型已加载")
    
    # 读取图像
    image = Image.open(args.image).convert("RGB")
    width, height = image.size
    print(f"图像尺寸: {width}x{height}")
    
    # 准备输入
    img_tensor = torch.from_numpy(np.array(image).transpose(2, 0, 1)).float().div(255.0).unsqueeze(0)
    img_tensor = img_tensor.to(args.device)
    
    # 模拟batch_data
    batch_data = [{"image": img_tensor, "height": height, "width": width, "ori_img": np.array(image)}]
    
    # 推理
    with torch.no_grad():
        outputs = model(batch_data)
        det_instances = outputs[0]["instances"]
    
    # 可视化
    result_image = visualize_detection(image, det_instances, args.threshold, args.output)
    
    # 使用matplotlib显示结果
    plt.figure(figsize=(12, 8))
    plt.imshow(result_image)
    plt.axis("off")
    plt.show()

if __name__ == "__main__":
    main() 