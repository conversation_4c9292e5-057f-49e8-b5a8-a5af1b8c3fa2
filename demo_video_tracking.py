"""
视频跟踪演示脚本
展示如何使用ByteTrack和ByteVisionTrack进行视频跟踪可视化

作者: AI Assistant
创建时间: 2025-06-29
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from video_tracker_visualizer import VideoTrackerVisualizer
from detectron2.utils.logger import setup_logger

def demo_bytetrack(input_video: str, output_dir: str = "./demo_output"):
    """
    演示ByteTrack跟踪
    
    Args:
        input_video: 输入视频路径
        output_dir: 输出目录
    """
    logger = logging.getLogger("demo")
    logger.info("=== ByteTrack 跟踪演示 ===")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 配置文件路径
    config_path = "configs/video_track_bytetrack_cfg.py"
    
    # 输出视频路径
    input_path = Path(input_video)
    output_video = os.path.join(output_dir, f"{input_path.stem}_bytetrack{input_path.suffix}")
    
    try:
        # 创建可视化器
        logger.info("初始化ByteTrack可视化器...")
        visualizer = VideoTrackerVisualizer(config_path)
        
        # 处理视频
        logger.info(f"开始处理视频: {input_video}")
        tracks = visualizer.process_video(
            input_video,
            output_video,
            display=True,  # 显示实时画面
            save_video=True  # 保存输出视频
        )
        
        logger.info(f"ByteTrack跟踪完成！输出视频: {output_video}")
        logger.info(f"总共跟踪了 {len(tracks)} 帧")
        
        return tracks, output_video
        
    except Exception as e:
        logger.error(f"ByteTrack跟踪过程中发生错误: {e}")
        raise


def demo_bytevision(input_video: str, output_dir: str = "./demo_output"):
    """
    演示ByteVisionTrack跟踪
    
    Args:
        input_video: 输入视频路径
        output_dir: 输出目录
    """
    logger = logging.getLogger("demo")
    logger.info("=== ByteVisionTrack 跟踪演示 ===")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 配置文件路径
    config_path = "configs/video_track_bytevision_cfg.py"
    
    # 输出视频路径
    input_path = Path(input_video)
    output_video = os.path.join(output_dir, f"{input_path.stem}_bytevision{input_path.suffix}")
    
    try:
        # 创建可视化器
        logger.info("初始化ByteVisionTrack可视化器...")
        visualizer = VideoTrackerVisualizer(config_path)
        
        # 处理视频
        logger.info(f"开始处理视频: {input_video}")
        tracks = visualizer.process_video(
            input_video,
            output_video,
            display=True,  # 显示实时画面
            save_video=True  # 保存输出视频
        )
        
        logger.info(f"ByteVisionTrack跟踪完成！输出视频: {output_video}")
        logger.info(f"总共跟踪了 {len(tracks)} 帧")
        
        return tracks, output_video
        
    except Exception as e:
        logger.error(f"ByteVisionTrack跟踪过程中发生错误: {e}")
        raise


def compare_trackers(input_video: str, output_dir: str = "./demo_output"):
    """
    比较两种跟踪器的效果
    
    Args:
        input_video: 输入视频路径
        output_dir: 输出目录
    """
    logger = logging.getLogger("demo")
    logger.info("=== 跟踪器比较演示 ===")
    
    # 检查输入视频是否存在
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频文件不存在: {input_video}")
    
    results = {}
    
    try:
        # 运行ByteTrack
        logger.info("开始ByteTrack跟踪...")
        bytetrack_tracks, bytetrack_output = demo_bytetrack(input_video, output_dir)
        results['bytetrack'] = {
            'tracks': bytetrack_tracks,
            'output': bytetrack_output,
            'total_frames': len(bytetrack_tracks)
        }
        
        # 运行ByteVisionTrack
        logger.info("开始ByteVisionTrack跟踪...")
        bytevision_tracks, bytevision_output = demo_bytevision(input_video, output_dir)
        results['bytevision'] = {
            'tracks': bytevision_tracks,
            'output': bytevision_output,
            'total_frames': len(bytevision_tracks)
        }
        
        # 打印比较结果
        logger.info("=== 跟踪器比较结果 ===")
        logger.info(f"ByteTrack:")
        logger.info(f"  - 输出视频: {bytetrack_output}")
        logger.info(f"  - 处理帧数: {results['bytetrack']['total_frames']}")
        
        logger.info(f"ByteVisionTrack:")
        logger.info(f"  - 输出视频: {bytevision_output}")
        logger.info(f"  - 处理帧数: {results['bytevision']['total_frames']}")
        
        return results
        
    except Exception as e:
        logger.error(f"跟踪器比较过程中发生错误: {e}")
        raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="视频跟踪演示程序")
    parser.add_argument("--input", "-i", required=True, help="输入视频路径")
    parser.add_argument("--output", "-o", default="./demo_output", help="输出目录")
    parser.add_argument("--tracker", "-t", choices=["bytetrack", "bytevision", "both"], 
                       default="both", help="要演示的跟踪器")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logger(name="demo")
    logger = logging.getLogger("demo")
    
    logger.info("视频跟踪演示程序启动")
    logger.info(f"输入视频: {args.input}")
    logger.info(f"输出目录: {args.output}")
    logger.info(f"跟踪器: {args.tracker}")
    
    try:
        if args.tracker == "bytetrack":
            demo_bytetrack(args.input, args.output)
        elif args.tracker == "bytevision":
            demo_bytevision(args.input, args.output)
        elif args.tracker == "both":
            compare_trackers(args.input, args.output)
        
        logger.info("演示完成！")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()


"""
使用示例:

1. 只运行ByteTrack:
   python demo_video_tracking.py --input video.mp4 --tracker bytetrack

2. 只运行ByteVisionTrack:
   python demo_video_tracking.py --input video.mp4 --tracker bytevision

3. 比较两种跟踪器:
   python demo_video_tracking.py --input video.mp4 --tracker both

4. 指定输出目录:
   python demo_video_tracking.py --input video.mp4 --output ./my_output --tracker both

注意事项:
- 确保已安装所有依赖项
- 确保模型权重文件存在
- 输入视频格式应为常见格式（mp4, avi等）
- 程序会在指定输出目录创建跟踪结果视频
"""
