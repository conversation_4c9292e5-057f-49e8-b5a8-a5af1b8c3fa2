import os
import subprocess
import time

def run_inference_command(gpu_id, config_file, num_gpus=1):
    """
    运行推理命令

    Args:
        gpu_id (int): GPU ID
        config_file (str): 配置文件路径
        num_gpus (int): 使用的 GPU 数量
    """
    command = f"CUDA_VISIBLE_DEVICES={gpu_id} python track.py --num-gpus {num_gpus} --config-file {config_file}"

    print(f"\n正在执行推理任务: {command}")
    try:
        process = subprocess.Popen(command, shell=True)
        process.wait()
        if process.returncode == 0:
            print(f"推理完成: {command}")
        else:
            print(f"推理失败: {command}")
    except Exception as e:
        print(f"执行出错: {str(e)}")


def main():
    # 配置要执行的推理任务列表
    
    
    inference_tasks = [
        # # MOT20
        {"gpu_id": 0, "config_file": "configs/mot20/mot20_ab_bytevision_cfg.py", "num_gpus": 1},# MOTA 84.4
        {"gpu_id": 0, "config_file": "configs/mot20/mot20_ab_bytetrack_cfg.py", "num_gpus": 1}, 
        # # DanceTrack
        # {"gpu_id": 0, "config_file": "configs/dancetrack/dancetrack_bytevision_cfg.py", "num_gpus": 1},
        # {"gpu_id": 0, "config_file": "configs/dancetrack/dancetrack_bytetrack_cfg.py", "num_gpus": 1},

    ]

    # 按顺序执行每个推理任务
    for task in inference_tasks:
        print(f"\n开始执行配置文件 {task['config_file']} 的推理任务...")
        run_inference_command(
            gpu_id=task["gpu_id"],
            config_file=task["config_file"],
            num_gpus=task["num_gpus"]
        )
        # 任务之间的等待时间
        time.sleep(2)  # 等待 2 秒再执行下一个任务


if __name__ == "__main__":
    main()