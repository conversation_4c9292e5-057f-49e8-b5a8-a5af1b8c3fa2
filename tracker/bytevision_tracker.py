import os
from typing import Union, List

import numpy as np
from collections import deque

import os.path as osp
import copy
import torch
import torch.nn.functional as F
from fast_reid.fast_reid_interfece import FastReIDInterface
from .kalman_filter import <PERSON><PERSON><PERSON>ilter

from .matching import *
from .basetrack import BaseTrack, TrackState


class STrack(BaseTrack):
    shared_kalman = KalmanFilter()

    def __init__(self, tlwh, score, feature):

        # wait activate
        self._tlwh = np.asarray(tlwh, dtype=np.float)
        self.track_positions = {}  # 存储历史位置
        self.kalman_filter = None
        self.mean, self.covariance = None, None
        self.is_activated = False

        # 深度特征
        self.deep_vector = self._get_deep_vec()
        self.feature = feature

        self.score = score
        self.tracklet_len = 0
        
        # 需要放在self.update_features()之前
        self.overlapped_sign = 0 # 0: not overlapped, 1: overlapped
        
    def set_overlapped_sign(self, sign):
        # 记录上一帧的遮挡状态
        self.was_occluded_last_frame = (self.overlapped_sign == 1)
        self.overlapped_sign = sign
        
    def get_overlapped_sign(self):
        return self.overlapped_sign

    def _get_deep_vec(self):
        cx = self._tlwh[0] + 0.5 * self._tlwh[2]
        y2 = self._tlwh[1] + self._tlwh[3]
        lendth = 2000 - y2
        return np.asarray([cx, y2, lendth], dtype=np.float)

    def predict(self):
        mean_state = self.mean.copy()
        if self.state != TrackState.Tracked:
            mean_state[6] = 0
            mean_state[7] = 0

        self.mean, self.covariance = self.kalman_filter.predict(mean_state, self.covariance)

    @staticmethod
    def multi_predict(stracks):
        if len(stracks) > 0:
            multi_mean = np.asarray([st.mean.copy() for st in stracks])
            multi_covariance = np.asarray([st.covariance for st in stracks])
            for i, st in enumerate(stracks):
                if st.state != TrackState.Tracked:
                    multi_mean[i][6] = 0
                    multi_mean[i][7] = 0
            multi_mean, multi_covariance = STrack.shared_kalman.multi_predict(multi_mean, multi_covariance)
            for i, (mean, cov) in enumerate(zip(multi_mean, multi_covariance)):
                stracks[i].mean = mean
                stracks[i].covariance = cov

    def activate(self, kalman_filter, frame_id):
        """Start a new tracklet"""
        self.kalman_filter = kalman_filter
        self.track_id = self.next_id()

        self.mean, self.covariance = self.kalman_filter.initiate(self.tlwh_to_xywh(self._tlwh))

        self.tracklet_len = 0
        self.state = TrackState.Tracked
        if frame_id == 1:
            self.is_activated = True
        self.frame_id = frame_id
        self.start_frame = frame_id

    def re_activate(self, new_track, frame_id, new_id=False):

        self.mean, self.covariance = self.kalman_filter.update(self.mean, self.covariance,
                                                               self.tlwh_to_xywh(new_track.tlwh))
        self.tracklet_len = 0
        self.state = TrackState.Tracked
        self.is_activated = True
        self.frame_id = frame_id
        if new_id:
            self.track_id = self.next_id()
        self.score = new_track.score

    def update(self, new_track, frame_id):
        """
        Update a matched track
        :type new_track: STrack
        :type frame_id: int
        :type update_feature: bool
        :return:
        """
        self.frame_id = frame_id
        self.tracklet_len += 1

        new_tlwh = new_track.tlwh
        # 保存当前帧的位置
        setattr(self, f'tlwh_{frame_id}', new_tlwh)

        self.mean, self.covariance = self.kalman_filter.update(self.mean, self.covariance, self.tlwh_to_xywh(new_tlwh))

        self.state = TrackState.Tracked
        self.is_activated = True

        self.score = new_track.score

    @staticmethod
    def multi_gmc(stracks, H=np.eye(2, 3)):
        if len(stracks) > 0:
            multi_mean = np.asarray([st.mean.copy() for st in stracks])
            multi_covariance = np.asarray([st.covariance for st in stracks])

            R = H[:2, :2]
            R8x8 = np.kron(np.eye(4, dtype=float), R)  # np.kron
            t = H[:2, 2]

            for i, (mean, cov) in enumerate(zip(multi_mean, multi_covariance)):
                mean = R8x8.dot(mean)
                mean[:2] += t
                cov = R8x8.dot(cov).dot(R8x8.transpose())

                stracks[i].mean = mean
                stracks[i].covariance = cov

    @property
    def tlwh(self):
        """Get current position in bounding box format `(top left x, top left y,
                width, height)`.
        """
        if self.mean is None:
            return self._tlwh.copy()
        ret = self.mean[:4].copy()
        ret[:2] -= ret[2:] / 2
        return ret

    @property
    def tlbr(self):
        """Convert bounding box to format `(min x, min y, max x, max y)`, i.e.,
        `(top left, bottom right)`.
        """
        ret = self.tlwh.copy()
        ret[2:] += ret[:2]
        return ret

    @property
    def xywh(self):
        """Convert bounding box to format `(min x, min y, max x, max y)`, i.e.,
        `(top left, bottom right)`.
        """
        ret = self.tlwh.copy()
        ret[:2] += ret[2:] / 2.0
        return ret

    @property
    # @jit(nopython=True)
    def deep_vec(self):
        """Convert bounding box to format `((top left, bottom right)`, i.e.,
        `(top left, bottom right)`.
        """
        ret = self.tlwh.copy()
        cx = ret[0] + 0.5 * ret[2]
        y2 = ret[1] + ret[3]
        lendth = 2000 - y2
        return np.asarray([cx, y2, lendth], dtype=np.float)

    @staticmethod
    # @jit(nopython=True)
    def tlwh_to_xyah(tlwh):
        """Convert bounding box to format `(center x, center y, aspect ratio,
        height)`, where the aspect ratio is `width / height`.
        """
        ret = np.asarray(tlwh).copy()
        ret[:2] += ret[2:] / 2
        ret[2] /= ret[3]
        return ret

    @staticmethod
    def tlwh_to_xywh(tlwh):
        """Convert bounding box to format `(center x, center y, width,
        height)`.
        """
        ret = np.asarray(tlwh).copy()
        ret[:2] += ret[2:] / 2
        return ret

    def to_xyah(self):
        return self.tlwh_to_xyah(self.tlwh)

    def to_xywh(self):
        return self.tlwh_to_xywh(self.tlwh)

    @staticmethod
    def tlbr_to_tlwh(tlbr):
        ret = np.asarray(tlbr).copy()
        ret[2:] -= ret[:2]
        return ret

    @staticmethod
    # @jit(nopython=True)
    def tlwh_to_tlbr(tlwh):
        ret = np.asarray(tlwh).copy()
        ret[2:] += ret[:2]
        return ret

    def __repr__(self):
        return 'OT_{}_({}-{})'.format(self.track_id, self.start_frame, self.end_frame)


class BYTEVisionTracker(object):
    def __init__(self, args, frame_rate=30):
        self.history_window = 30
        self.tracked_stracks = []
        self.lost_stracks = []
        self.removed_stracks = []
        self.frame_id = 0
        self.args = args
        self.det_thresh = args.track_thresh + 0.1
        self.buffer_size = int(frame_rate / 30.0 * args.track_buffer)
        self.max_time_lost = self.buffer_size
        self.kalman_filter = KalmanFilter()
        # ReID module
        self.proximity_thresh = args.proximity_thresh
        self.appearance_thresh = args.appearance_thresh

        self.encoder = FastReIDInterface(args.fast_reid_config, args.fast_reid_weights, args.device)

        print("This is ByteVision")

    def update(self, output_results, curr_img=None):
        self.frame_id += 1
        activated_starcks = []
        refind_stracks = []
        lost_stracks = []
        removed_stracks = []
        
        # current detections
        bboxes = output_results.pred_boxes.tensor.cpu().numpy()  # x1y1x2y2
        scores = output_results.scores.cpu().numpy()
        features = self.encoder.inference(curr_img, bboxes)

        remain_inds = scores > self.args.track_thresh
        inds_low = scores > 0.1
        inds_high = scores < self.args.track_thresh

        inds_second = np.logical_and(inds_low, inds_high)

        dets = bboxes[remain_inds]
        scores_keep = scores[remain_inds]
        features_keep = features[remain_inds]

        scores_second = scores[inds_second]
        dets_second = bboxes[inds_second]
        features_second = features[inds_second]

        if len(dets) > 0:
            '''Detections'''
            detections = [STrack(STrack.tlbr_to_tlwh(tlbr), s, f) for
                          (tlbr, s, f) in zip(dets, scores_keep, features_keep)]
        else:
            detections = []

        ''' Add newly detected tracklets to tracked_stracks'''
        unconfirmed = []
        tracked_stracks = []  # type: list[STrack]
        for track in self.tracked_stracks:
            if not track.is_activated:
                unconfirmed.append(track)
            else:
                tracked_stracks.append(track)

        ''' Step 2: First association, with high score detection boxes'''
        strack_pool = joint_stracks(tracked_stracks, self.lost_stracks)
        # Predict the current location with KF
        STrack.multi_predict(strack_pool)
        
        # 获取遮挡和非遮挡的轨迹和检测
        overlapped_trks, unoverlapped_trks = self.get_overlapped(strack_pool)
        overlapped_dets, unoverlapped_dets = self.get_overlapped(detections)

        # 第一轮：特征匹配（非遮挡目标）
        if len(unoverlapped_trks) > 0 and len(unoverlapped_dets) > 0:
            F_dists = embedding_distance(unoverlapped_trks, unoverlapped_dets)
            if not self.args.mot20:
                F_dists = fuse_score(F_dists, unoverlapped_dets)
            matches_1, u_track_1, u_detection_1 = linear_assignment(F_dists, thresh=0.4)
        else:
            matches_1, u_track_1, u_detection_1 = [], list(range(len(unoverlapped_trks))), list(range(len(unoverlapped_dets)))

        for itracked, idet in matches_1:
            track = unoverlapped_trks[itracked]
            det = unoverlapped_dets[idet]
            if track.state == TrackState.Tracked:
                track.update(det, self.frame_id)
                activated_starcks.append(track)
            else:
                track.re_activate(det, self.frame_id, new_id=False)
                refind_stracks.append(track)
                
        # 收集第一轮匹配后的剩余轨迹和检测
        remaining_unoverlapped_tracks = [unoverlapped_trks[i] for i in u_track_1 if unoverlapped_trks[i].state == TrackState.Tracked]
        remaining_unoverlapped_dets = [unoverlapped_dets[i] for i in u_detection_1]
        
        # 第二轮：IoU匹配（剩余的非遮挡目标 + 所有遮挡目标）
        r_tracked_stracks_1 = remaining_unoverlapped_tracks + overlapped_trks
        r_detections_1 = remaining_unoverlapped_dets + overlapped_dets
        
        if len(r_tracked_stracks_1) > 0 and len(r_detections_1) > 0:
            I_dists = iou_distance(r_tracked_stracks_1, r_detections_1)
            matches_2, u_track_2, u_detection_2 = linear_assignment(I_dists, thresh=0.7)
        else:
            matches_2, u_track_2, u_detection_2 = [], list(range(len(r_tracked_stracks_1))), list(range(len(r_detections_1)))
        
        for itracked, idet in matches_2:
            track = r_tracked_stracks_1[itracked]
            det = r_detections_1[idet]
            if track.state == TrackState.Tracked:
                track.update(det, self.frame_id)
                activated_starcks.append(track)
            else:
                track.re_activate(det, self.frame_id, new_id=False)
                refind_stracks.append(track)

        ''' Step 3: Second association, with low score detection boxes'''
        if len(dets_second) > 0:
            detections_second = [STrack(STrack.tlbr_to_tlwh(tlbr), s, f) for
                                 (tlbr, s, f) in zip(dets_second, scores_second, features_second)]
        else:
            detections_second = []

        # 收集第二轮后的剩余轨迹
        r_tracked_stracks_2 = [r_tracked_stracks_1[i] for i in u_track_2 if r_tracked_stracks_1[i].state == TrackState.Tracked]
        
        if len(r_tracked_stracks_2) > 0 and len(detections_second) > 0:
            F_dists_2 = embedding_distance(r_tracked_stracks_2, detections_second)
            matches_3, u_track_3, u_detection_3 = linear_assignment(F_dists_2, thresh=0.2)
        else:
            matches_3, u_track_3, u_detection_3 = [], list(range(len(r_tracked_stracks_2))), list(range(len(detections_second)))

        for itracked, idet in matches_3:
            track = r_tracked_stracks_2[itracked]
            det = detections_second[idet]
            if track.state == TrackState.Tracked:
                track.update(det, self.frame_id)
                activated_starcks.append(track)
            else:
                track.re_activate(det, self.frame_id, new_id=False)
                refind_stracks.append(track)
                
        # 第四轮：剩余轨迹与剩余高置信度检测的IoU匹配
        r_tracked_stracks_3 = [r_tracked_stracks_2[i] for i in u_track_3 if r_tracked_stracks_2[i].state == TrackState.Tracked]
        r_detections_3 = [r_detections_1[i] for i in u_detection_2]
        
        if len(r_tracked_stracks_3) > 0 and len(r_detections_3) > 0:
            I_dists_2 = iou_distance(r_tracked_stracks_3, r_detections_3)
            matches_4, u_track_4, u_detection_4 = linear_assignment(I_dists_2, thresh=0.6)
        else:
            matches_4, u_track_4, u_detection_4 = [], list(range(len(r_tracked_stracks_3))), list(range(len(r_detections_3)))
        
        for itracked, idet in matches_4:
            track = r_tracked_stracks_3[itracked]
            det = r_detections_3[idet]
            if track.state == TrackState.Tracked:
                track.update(det, self.frame_id)
                activated_starcks.append(track)
            else:
                track.re_activate(det, self.frame_id, new_id=False)
                refind_stracks.append(track)

        # 标记丢失的轨迹
        for it in u_track_4:
            track = r_tracked_stracks_3[it]
            if not track.state == TrackState.Lost:
                track.mark_lost()
                lost_stracks.append(track)

        '''Deal with unconfirmed tracks, usually tracks with only one beginning frame'''
        final_detections = [r_detections_3[i] for i in u_detection_4]
        
        if len(unconfirmed) > 0 and len(final_detections) > 0:
            dists = iou_distance(unconfirmed, final_detections)
            if not self.args.mot20:
                dists = fuse_score(dists, final_detections)
            matches, u_unconfirmed, u_detection = linear_assignment(dists, thresh=0.7)
        else:
            matches, u_unconfirmed, u_detection = [], list(range(len(unconfirmed))), list(range(len(final_detections)))
            
        for itracked, idet in matches:
            unconfirmed[itracked].update(final_detections[idet], self.frame_id)
            activated_starcks.append(unconfirmed[itracked])
        for it in u_unconfirmed:
            track = unconfirmed[it]
            track.mark_removed()
            removed_stracks.append(track)

        """ Step 4: Init new stracks"""
        for inew in u_detection:
            track = final_detections[inew]
            if track.score < self.det_thresh:
                continue
            track.activate(self.kalman_filter, self.frame_id)
            activated_starcks.append(track)
            
        """ Step 5: Update state"""
        for track in self.lost_stracks:
            if self.frame_id - track.end_frame > self.max_time_lost:
                track.mark_removed()
                removed_stracks.append(track)

        self.tracked_stracks = [t for t in self.tracked_stracks if t.state == TrackState.Tracked]
        self.tracked_stracks = joint_stracks(self.tracked_stracks, activated_starcks)
        self.tracked_stracks = joint_stracks(self.tracked_stracks, refind_stracks)
        self.lost_stracks = sub_stracks(self.lost_stracks, self.tracked_stracks)
        self.lost_stracks.extend(lost_stracks)
        self.lost_stracks = sub_stracks(self.lost_stracks, self.removed_stracks)
        self.removed_stracks.extend(removed_stracks)
        self.tracked_stracks, self.lost_stracks = remove_duplicate_stracks(self.tracked_stracks, self.lost_stracks)
        
        # get scores of lost tracks
        output_stracks = [track for track in self.tracked_stracks if track.is_activated]

        return output_stracks
    
    def get_overlapped(self, trks):
        """
        使用伪深度信息来判断遮挡关系
        在有遮挡的目标组中，最前面的目标（y2最大）被认为是未被遮挡的
        """
        if len(trks) <= 1:
            for trk in trks:
                trk.set_overlapped_sign(0)
            return [], trks
        
        # 计算IoU距离矩阵
        overlap_dists = iou_distance(trks, trks)
        
        # 构建遮挡关系图
        overlapped_groups = []
        processed_indices = set()
        
        for i in range(len(trks)):
            if i in processed_indices:
                continue
            
            current_group = {i}
            for j in range(len(trks)):
                # 修正遮挡判断逻辑：IoU距离小于0.8意味着IoU大于0.2
                if i != j and overlap_dists[i, j] < 0.5:  # IoU > 0.2
                    current_group.add(j)
            
            if len(current_group) > 1:
                overlapped_groups.append(current_group)
                processed_indices.update(current_group)
        
        # 处理每个遮挡组
        overlapped_indices = set()
        
        for group in overlapped_groups:
            # 按底部y坐标排序（深度排序）
            group_depths = [(idx, trks[idx].tlbr[3]) for idx in group]
            group_depths.sort(key=lambda x: x[1], reverse=True)
            
            # 最前面的未被遮挡，其他被遮挡
            for i, (idx, _) in enumerate(group_depths):
                if i > 0:  # 除了最前面的，其他都被遮挡
                    overlapped_indices.add(idx)
        
        # 设置遮挡状态
        for i, trk in enumerate(trks):
            trk.set_overlapped_sign(1 if i in overlapped_indices else 0)
        
        # 分离轨迹
        overlapped_trks = [trks[i] for i in overlapped_indices]
        unoverlapped_trks = [trks[i] for i in range(len(trks)) if i not in overlapped_indices]
        
        return overlapped_trks, unoverlapped_trks


def joint_stracks(tlista, tlistb):
    exists = {}
    res = []
    for t in tlista:
        exists[t.track_id] = 1
        res.append(t)
    for t in tlistb:
        tid = t.track_id
        if not exists.get(tid, 0):
            exists[tid] = 1
            res.append(t)
    return res


def sub_stracks(tlista, tlistb):
    stracks = {}
    for t in tlista:
        stracks[t.track_id] = t
    for t in tlistb:
        tid = t.track_id
        if stracks.get(tid, 0):
            del stracks[tid]
    return list(stracks.values())


def remove_duplicate_stracks(stracksa, stracksb):
    pdist = iou_distance(stracksa, stracksb)
    pairs = np.where(pdist < 0.15)
    dupa, dupb = list(), list()
    for p, q in zip(*pairs):
        timep = stracksa[p].frame_id - stracksa[p].start_frame
        timeq = stracksb[q].frame_id - stracksb[q].start_frame
        if timep > timeq:
            dupb.append(q)
        else:
            dupa.append(p)
    resa = [t for i, t in enumerate(stracksa) if not i in dupa]
    resb = [t for i, t in enumerate(stracksb) if not i in dupb]
    return resa, resb