import cv2
import os
import numpy as np
import torch
import logging, tqdm
import numpy as np
from .timer import Timer
from collections import defaultdict
from detectron2.utils import comm
from detectron2.structures import Instances, Boxes
from  tracker.byte_tracker import BYTETracker
from  tracker.sparse_tracker import SparseTracker
from tracker.bytevision_tracker import BYTEVisionTracker
from  tracker.sort import Sort
from  tracker.oc_sort import OCSort
# 导入跟踪器工具函数
from utils.tracker_utils import determine_tracker_type

logger = logging.getLogger("detectron2")

def write_results(filename, results):
    save_format = '{frame},{id},{x1},{y1},{w},{h},{s},-1,-1,-1\n'
    with open(filename, 'w') as f:
        for frame_id, tlwhs, track_ids, scores in results:
            for tlwh, track_id, score in zip(tlwhs, track_ids, scores):
                if track_id < 0:
                    continue
                x1, y1, w, h = tlwh
                line = save_format.format(frame=frame_id, id=track_id, x1=round(x1, 1), y1=round(y1, 1), w=round(w, 1), h=round(h, 1), s=round(score, 2))
                f.write(line)
    logger.info('save results to {}'.format(filename))


def write_results_no_score(filename, results):
    save_format = '{frame},{id},{x1},{y1},{w},{h},-1,-1,-1,-1\n'
    with open(filename, 'w') as f:
        for frame_id, tlwhs, track_ids in results:
            for tlwh, track_id in zip(tlwhs, track_ids):
                if track_id < 0:
                    continue
                x1, y1, w, h = tlwh
                line = save_format.format(frame=frame_id, id=track_id, x1=round(x1, 1), y1=round(y1, 1), w=round(w, 1), h=round(h, 1))
                f.write(line)
    logger.info('save results to {}'.format(filename))


class MOTEvaluator:
    """
    COCO AP Evaluation class.  All the data in the val2017 dataset are processed
    and evaluated by COCO API.
    """

    def __init__(
        self, args, dataloader):
        """
        Args:
            dataloader (Dataloader): evaluate dataloader.
        """
        self.dataloader = dataloader
        self.args = args

    def evaluate(
        self,
        model,
        half=False,
        result_folder=None
    ):
        """
        COCO average precision (AP) Evaluation. Iterate inference on the test dataset
        and the results are evaluated by COCO API.

        NOTE: This function will change training mode to False, please save states if needed.

        Args:
            model : model to evaluate.

        Returns:
            ap50_95 (float) : COCO AP of IoU=50:95
            ap50 (float) : COCO AP of IoU=50
            summary (sr): summary info of evaluation.
        """
        # TODO half to amp_test
        tensor_type = torch.cuda.HalfTensor if half else torch.cuda.FloatTensor
        model = model.eval()
        if half:
            model = model.half()
        results = []
        timer_avgs, timer_calls = [], []
        video_names = defaultdict()
        progress_bar = tqdm if comm.is_main_process() else iter

        # 获取跟踪器类型 - 支持布尔标志和直接指定
        tracker_type = determine_tracker_type(self.args.track)
        logger.info(f"使用跟踪器类型: {tracker_type}")

        # 根据跟踪器类型创建跟踪器
        if tracker_type == "ByteTracker":
            tracker = BYTETracker(self.args.track)
        elif tracker_type == "SparseTracker":
            tracker = SparseTracker(self.args.track)
        elif tracker_type == "BYTEVisionTracker":
            tracker = BYTEVisionTracker(self.args.track)
        elif tracker_type == "Sort":
            tracker = Sort(0.2)  # for 17-0.3 20-0.2
        elif tracker_type == "OCSort":
            tracker = OCSort(0.4)  # for 17-0.6 20-0.4
        else:
            logger.warning(f"未知的跟踪器类型 '{tracker_type}'，使用默认的BYTETracker")
            tracker = BYTETracker(self.args.track)

        timer = Timer()
        ori_thresh = self.args.track.track_thresh
        ori_track_buffer = self.args.track.track_buffer
        video_id = 0

        for cur_iter, batch_data in enumerate(
            progress_bar.tqdm(self.dataloader)
        ):
            with torch.no_grad():
                # init tracker
                frame_id = int(batch_data[0]["frame_id"])
                if frame_id == 1:
                    video_id += 1
                img_file_name = batch_data[0]["file_name"]
                video_name = img_file_name.split('/')[-3]

                # 如果is_public是true的话那么就加载的是det，直接载入文件
                if self.args.track.is_public:
                    if frame_id ==1:
                        det_path = os.path.join(batch_data[0]["file_name"][:-10].replace('img1', 'det'), 'det.txt')
                        print("************************************",det_path,"******************************************")
                        video_dets = np.loadtxt(det_path, dtype=np.float32, delimiter=',')
                        print("*******************************",video_dets,"*********************************************")

                # if do ablation please invalidate the specific thresh settings from 124 - 138
                if 'MOT17-05-' in video_name  or 'MOT17-06-' in video_name:
                    self.args.track.track_buffer = 14
                elif 'MOT17-13-' in video_name:
                    self.args.track.track_buffer = 25
                else:
                    self.args.track.track_buffer = ori_track_buffer

                if 'MOT17-06-' in video_name:
                    self.args.track.track_thresh = 0.65
                elif 'MOT17-12-' in video_name:
                    self.args.track.track_thresh = 0.7
                elif video_name in ['MOT20-06', 'MOT20-08']:
                    self.args.track.track_thresh = 0.27
                else:
                    self.args.track.track_thresh = ori_thresh

                if video_name not in video_names:
                    video_names[video_id] = video_name
                if frame_id == 1:
                    # 重新确定跟踪器类型（可能配置已更改）
                    tracker_type = determine_tracker_type(self.args.track)
                    logger.info(f"开始新视频，重新初始化跟踪器: {tracker_type}")

                    if tracker_type == "ByteTracker":
                        tracker = BYTETracker(self.args.track)
                    elif tracker_type == "SparseTracker":
                        tracker = SparseTracker(self.args.track)
                    elif tracker_type == "BYTEVisionTracker":
                        tracker = BYTEVisionTracker(self.args.track)
                    elif tracker_type == "Sort":
                        tracker = Sort(0.2)  # for 17-0.3 20-0.2
                    elif tracker_type == "OCSort":
                        tracker = OCSort(0.4)  # for 17-0.6 20-0.4
                    else:
                        logger.warning(f"未知的跟踪器类型 '{tracker_type}'，使用默认的BYTETracker")
                        tracker = BYTETracker(self.args.track)

                    if len(results) != 0:
                        result_filename = os.path.join(result_folder, '{}.txt'.format(video_names[video_id - 1]))
                        write_results(result_filename, results)
                        results = []
                    timer_avgs.append(timer.average_time)
                    timer_calls.append(timer.calls)
                    timer.clear()

                if 'MOT17-06-' in video_name:
                    tracker.down_scale = 2
                if 'MOT17-01-' in video_name:
                    tracker.layers = 2

                # run model
                timer.tic()
                if not self.args.track.is_public:
                    batch_data[0]["image"] = batch_data[0]["image"].type(tensor_type)
                    #  调用模型进行检测
                    outputs = model(batch_data)

                    # 添加可视化代码 - 保存检测结果
                    if hasattr(self.args.track, 'visualize') and self.args.track.visualize and frame_id % 5 == 0:
                        try:
                            # 创建可视化目录
                            vis_folder = os.path.join(result_folder, "visualization")
                            os.makedirs(vis_folder, exist_ok=True)

                            # 获取原始图像
                            img = batch_data[0]["ori_img"].copy()

                            # 获取检测结果
                            det_instances = outputs[0]["instances"]

                            # 准备颜色列表 (BGR格式)
                            colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (255, 255, 0), (0, 255, 255), (255, 0, 255)]

                            # 画出检测框
                            boxes = det_instances.pred_boxes.tensor.cpu().numpy()
                            scores = det_instances.scores.cpu().numpy()

                            # 打印检测信息
                            print(f"【可视化】视频: {video_name}, 帧: {frame_id}, 检测到 {len(boxes)} 个目标")
                            if len(boxes) > 0:
                                # 打印前5个检测框和分数
                                num_to_show = min(5, len(boxes))
                                print(f"【可视化】前{num_to_show}个目标检测框和得分:")
                                for i in range(num_to_show):
                                    print(f"  框 {i+1}: {boxes[i].tolist()}, 得分: {scores[i]:.4f}")

                            for i, (box, score) in enumerate(zip(boxes, scores)):
                                x1, y1, x2, y2 = map(int, box)
                                color = colors[i % len(colors)]

                                # 画矩形框
                                cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)

                                # 添加文本标签
                                label = f"{score:.2f}"
                                cv2.putText(img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

                            # 保存图像
                            save_path = os.path.join(vis_folder, f"{video_name}_{frame_id:06d}.jpg")
                            cv2.imwrite(save_path, img)

                            # 如果是第一帧，打印保存位置
                            if frame_id <= 5:
                                print(f"【可视化】检测结果图像已保存到: {save_path}")
                        except Exception as e:
                            print(f"可视化过程中发生错误: {e}")
                else:
                    frame_mask = video_dets[:, 0] == frame_id
                    frame_dets = video_dets[frame_mask] # 69, -1, 912.8, 482.9, 97.6, 112.6, 1
                    frame_dets[:, 4] = frame_dets[:, 4] + frame_dets[:, 2]
                    frame_dets[:, 5] = frame_dets[:, 5] + frame_dets[:, 3]

                    frame_dets = torch.from_numpy(frame_dets)
                    det_instances = Instances((1,1))
                    det_instances.pred_boxes = Boxes(frame_dets[:, 2:6])
                    det_instances.scores = frame_dets[:, 6]

            # run tracking
            if not self.args.track.is_public:
                det_instances = outputs[0]["instances"]

            if det_instances is not None:
                # 分析检测结果结构和数据类型
                if hasattr(self.args.track, 'debug') and self.args.track.debug:
                    print("检测结果分析:")
                    print(f"  - 类型: {type(det_instances)}")
                    print(f"  - pred_boxes类型: {type(det_instances.pred_boxes)}")
                    print(f"  - scores类型: {type(det_instances.scores)}")
                    print(f"  - scores形状: {det_instances.scores.shape}")
                    if len(det_instances.scores) > 0:
                        print(f"  - 第一个得分: {det_instances.scores[0].item():.4f}")
                        print(f"  - 所有得分范围: {det_instances.scores.min().item():.4f}-{det_instances.scores.max().item():.4f}")

                    if hasattr(det_instances, 'pred_classes'):
                        print(f"  - 检测类别: {det_instances.pred_classes}")

                online_targets = tracker.update(
                    det_instances, batch_data[0]["ori_img"]
                )
                online_tlwhs = []
                online_ids = []
                online_scores = []
                for t in online_targets:
                    # 根据跟踪器类型区分处理方式
                    if tracker_type in ["Sort", "OCSort"]:
                        tlwh = t[:4]
                        tid = int(t[4])
                        score = t[5]
                    else:
                        # 对于标准格式，直接获取TLWH格式的框
                        tlwh = t.tlwh
                        tid = t.track_id
                        score = t.score

                    vertical = tlwh[2] / tlwh[3] > 1.6
                    if tlwh[2] * tlwh[3] > self.args.track.min_box_area and not vertical:
                        online_tlwhs.append(tlwh)
                        online_ids.append(tid)
                        online_scores.append(score)

                # 保存结果前添加调试信息
                # print(f"【调试】当前帧 {frame_id} 跟踪结果: {len(online_tlwhs)} 个跟踪目标")
                # if len(online_tlwhs) > 0:
                #     print(f"【调试】第一个跟踪结果: {online_tlwhs[0]}, ID: {online_ids[0]}, 得分: {online_scores[0]:.4f}")

                # save results
                results.append((frame_id, online_tlwhs, online_ids, online_scores))
            timer.toc()
            # if frame_id % 20 == 0:
            #     logger.info('Processing frame {} ({:.2f} fps)'.format(frame_id, 1. / max(1e-5, timer.average_time)))
            if cur_iter == len(self.dataloader) - 1:
                result_filename = os.path.join(result_folder, '{}.txt'.format(video_names[video_id]))
                write_results(result_filename, results)
                timer_avgs.append(timer.average_time)
                timer_calls.append(timer.calls)

        timer_avgs = np.asarray(timer_avgs)
        timer_calls = np.asarray(timer_calls)
        all_time = np.dot(timer_avgs, timer_calls)
        avg_time = all_time / np.sum(timer_calls)
        logger.info('Time elapsed: {:.2f} seconds, FPS: {:.2f}'.format(all_time, 1.0 / avg_time))