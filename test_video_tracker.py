"""
视频跟踪器测试脚本
用于验证video_tracker_visualizer.py的功能

作者: AI Assistant
创建时间: 2025-06-29
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from detectron2.utils.logger import setup_logger

def create_test_video(output_path: str, duration: int = 5, fps: int = 30) -> str:
    """
    创建一个测试视频文件
    
    Args:
        output_path: 输出视频路径
        duration: 视频时长（秒）
        fps: 帧率
        
    Returns:
        str: 创建的视频文件路径
    """
    # 视频参数
    width, height = 640, 480
    total_frames = duration * fps
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 生成测试帧
    for frame_idx in range(total_frames):
        # 创建黑色背景
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加移动的矩形（模拟人）
        t = frame_idx / total_frames
        
        # 第一个目标：从左到右移动
        x1 = int(50 + t * (width - 150))
        y1 = 200
        cv2.rectangle(frame, (x1, y1), (x1 + 80, y1 + 120), (0, 255, 0), -1)
        cv2.putText(frame, "Person1", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 第二个目标：从右到左移动
        x2 = int(width - 50 - t * (width - 150))
        y2 = 300
        cv2.rectangle(frame, (x2, y2), (x2 + 80, y2 + 120), (255, 0, 0), -1)
        cv2.putText(frame, "Person2", (x2, y2 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 添加帧信息
        cv2.putText(frame, f"Frame: {frame_idx + 1}/{total_frames}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 写入帧
        out.write(frame)
    
    # 释放资源
    out.release()
    
    print(f"测试视频已创建: {output_path}")
    return output_path


def test_tracker_utils():
    """测试跟踪器工具函数"""
    print("=== 测试跟踪器工具函数 ===")
    
    try:
        from utils.tracker_utils import determine_tracker_type, generate_colors
        
        # 测试跟踪器类型确定
        class MockConfig:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)
        
        # 测试ByteTracker
        config1 = MockConfig(byte=True, byte_vision=False)
        tracker_type1 = determine_tracker_type(config1)
        assert tracker_type1 == "ByteTracker", f"期望ByteTracker，得到{tracker_type1}"
        
        # 测试ByteVisionTracker
        config2 = MockConfig(byte=False, byte_vision=True)
        tracker_type2 = determine_tracker_type(config2)
        assert tracker_type2 == "BYTEVisionTracker", f"期望BYTEVisionTracker，得到{tracker_type2}"
        
        # 测试直接指定tracker_type
        config3 = MockConfig(tracker_type="ByteTracker")
        tracker_type3 = determine_tracker_type(config3)
        assert tracker_type3 == "ByteTracker", f"期望ByteTracker，得到{tracker_type3}"
        
        # 测试颜色生成
        colors = generate_colors(5)
        assert len(colors) == 5, f"期望5种颜色，得到{len(colors)}种"
        assert all(len(color) == 3 for color in colors), "每种颜色应该有3个分量"
        
        print("✓ 跟踪器工具函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 跟踪器工具函数测试失败: {e}")
        return False


def test_config_files():
    """测试配置文件"""
    print("=== 测试配置文件 ===")
    
    config_files = [
        "configs/video_track_bytetrack_cfg.py",
        "configs/video_track_bytevision_cfg.py"
    ]
    
    success = True
    
    for config_file in config_files:
        try:
            if not os.path.exists(config_file):
                print(f"✗ 配置文件不存在: {config_file}")
                success = False
                continue
            
            # 尝试加载配置
            from detectron2.config import LazyConfig
            cfg = LazyConfig.load(config_file)
            
            # 检查必要的配置项
            assert hasattr(cfg, 'model'), f"{config_file} 缺少model配置"
            assert hasattr(cfg, 'train'), f"{config_file} 缺少train配置"
            assert hasattr(cfg, 'track'), f"{config_file} 缺少track配置"
            
            print(f"✓ 配置文件测试通过: {config_file}")
            
        except Exception as e:
            print(f"✗ 配置文件测试失败 {config_file}: {e}")
            success = False
    
    return success


def test_video_creation():
    """测试视频创建功能"""
    print("=== 测试视频创建功能 ===")
    
    try:
        # 创建临时视频文件
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        # 创建测试视频
        created_path = create_test_video(test_video_path, duration=2, fps=10)
        
        # 验证视频文件
        assert os.path.exists(created_path), "测试视频文件未创建"
        
        # 验证视频可以打开
        cap = cv2.VideoCapture(created_path)
        assert cap.isOpened(), "无法打开创建的测试视频"
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()
        
        assert frame_count == 20, f"期望20帧，得到{frame_count}帧"
        assert abs(fps - 10) < 1, f"期望10fps，得到{fps}fps"
        
        # 清理
        os.unlink(created_path)
        
        print("✓ 视频创建功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 视频创建功能测试失败: {e}")
        return False


def test_imports():
    """测试关键模块导入"""
    print("=== 测试模块导入 ===")
    
    modules_to_test = [
        ("utils.tracker_utils", ["determine_tracker_type", "generate_colors", "draw_tracking_results"]),
        ("video_tracker_visualizer", ["VideoTrackerVisualizer"]),
        ("demo_video_tracking", ["demo_bytetrack", "demo_bytevision"]),
    ]
    
    success = True
    
    for module_name, functions in modules_to_test:
        try:
            module = __import__(module_name, fromlist=functions)
            
            for func_name in functions:
                assert hasattr(module, func_name), f"{module_name} 缺少函数 {func_name}"
            
            print(f"✓ 模块导入测试通过: {module_name}")
            
        except Exception as e:
            print(f"✗ 模块导入测试失败 {module_name}: {e}")
            success = False
    
    return success


def run_basic_functionality_test():
    """运行基本功能测试"""
    print("=== 基本功能测试 ===")
    
    try:
        # 创建测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        create_test_video(test_video_path, duration=1, fps=5)  # 短视频用于测试
        
        # 测试配置加载
        from detectron2.config import LazyConfig
        config_path = "configs/video_track_bytetrack_cfg.py"
        
        if not os.path.exists(config_path):
            print(f"✗ 配置文件不存在: {config_path}")
            return False
        
        cfg = LazyConfig.load(config_path)
        
        # 测试跟踪器类型确定
        from utils.tracker_utils import determine_tracker_type
        tracker_type = determine_tracker_type(cfg.track)
        
        print(f"✓ 确定的跟踪器类型: {tracker_type}")
        
        # 清理
        os.unlink(test_video_path)
        
        print("✓ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始视频跟踪器测试...")
    
    # 设置日志
    setup_logger(name="test")
    
    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("跟踪器工具函数", test_tracker_utils),
        ("配置文件", test_config_files),
        ("视频创建功能", test_video_creation),
        ("基本功能", run_basic_functionality_test),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 打印测试结果摘要
    print(f"\n{'='*50}")
    print("测试结果摘要")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("❌ 部分测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
