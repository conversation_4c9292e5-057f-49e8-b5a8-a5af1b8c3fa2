# 视频跟踪可视化程序使用指南

本项目新增了视频跟踪可视化功能，支持ByteTrack和ByteVisionTrack两种跟踪器，能够对输入视频进行目标跟踪并生成带有跟踪框和ID标签的可视化结果。

## 功能特点

- 🎯 支持ByteTrack和ByteVisionTrack两种先进的跟踪算法
- 🎨 精美的可视化效果，包括彩色跟踪框和ID标签
- 📹 支持多种视频格式输入和输出
- ⚡ 实时显示跟踪过程
- 📊 详细的跟踪统计信息
- 🔧 灵活的配置系统

## 文件结构

```
├── video_tracker_visualizer.py      # 主要的视频跟踪可视化程序
├── demo_video_tracking.py           # 演示脚本
├── utils/tracker_utils.py           # 跟踪器工具函数
├── configs/
│   ├── video_track_bytetrack_cfg.py    # ByteTrack配置文件
│   └── video_track_bytevision_cfg.py   # ByteVisionTrack配置文件
└── VIDEO_TRACKING_README.md         # 本说明文件
```

## 安装依赖

确保已安装以下依赖：

```bash
pip install opencv-python torch torchvision detectron2
```

## 快速开始

### 1. 使用演示脚本（推荐）

最简单的使用方式是运行演示脚本：

```bash
# 比较两种跟踪器效果
python demo_video_tracking.py --input your_video.mp4 --tracker both

# 只使用ByteTrack
python demo_video_tracking.py --input your_video.mp4 --tracker bytetrack

# 只使用ByteVisionTrack
python demo_video_tracking.py --input your_video.mp4 --tracker bytevision
```

### 2. 直接使用主程序

```bash
# 使用ByteTrack跟踪
python video_tracker_visualizer.py --input video.mp4 --tracker bytetrack --output output_bytetrack.mp4

# 使用ByteVisionTrack跟踪
python video_tracker_visualizer.py --input video.mp4 --tracker bytevision --output output_bytevision.mp4
```

## 详细使用说明

### 命令行参数

#### video_tracker_visualizer.py

```bash
python video_tracker_visualizer.py [参数]

必需参数:
  --input, -i          输入视频路径

可选参数:
  --output, -o         输出视频路径（默认自动生成）
  --tracker, -t        跟踪器类型 [bytetrack|bytevision]
  --config, -c         自定义配置文件路径
  --weights, -w        自定义模型权重路径
  --no-display         不显示实时画面
  --no-save           不保存输出视频
```

#### demo_video_tracking.py

```bash
python demo_video_tracking.py [参数]

必需参数:
  --input, -i          输入视频路径

可选参数:
  --output, -o         输出目录（默认: ./demo_output）
  --tracker, -t        跟踪器类型 [bytetrack|bytevision|both]
```

### 配置文件说明

#### ByteTrack配置 (configs/video_track_bytetrack_cfg.py)

- 使用IoU匹配进行目标关联
- 适合快速跟踪场景
- 计算效率高

#### ByteVisionTrack配置 (configs/video_track_bytevision_cfg.py)

- 结合外观特征和IoU匹配
- 使用FastReID进行重识别
- 在遮挡和相似目标场景下表现更好

### 可视化效果

程序会在跟踪框上显示：
- 🎨 不同颜色的边界框（每个ID对应一种颜色）
- 🏷️ 目标ID标签
- 📊 置信度分数（可选）
- ℹ️ 帧信息和跟踪统计

## 使用示例

### 示例1：基本跟踪

```bash
# 对视频进行ByteTrack跟踪
python video_tracker_visualizer.py --input sample_video.mp4 --tracker bytetrack
```

输出：
- 实时显示跟踪过程
- 生成 `sample_video_bytetrack_tracked.mp4`
- 打印跟踪统计信息

### 示例2：批量比较

```bash
# 同时运行两种跟踪器进行比较
python demo_video_tracking.py --input sample_video.mp4 --tracker both --output ./results
```

输出：
- `./results/sample_video_bytetrack.mp4`
- `./results/sample_video_bytevision.mp4`
- 详细的比较统计信息

### 示例3：自定义配置

```bash
# 使用自定义配置和权重
python video_tracker_visualizer.py \
    --input video.mp4 \
    --config my_config.py \
    --weights my_weights.pth \
    --output custom_output.mp4
```

## 性能优化建议

1. **GPU加速**: 确保CUDA可用以获得最佳性能
2. **视频分辨率**: 较大的视频可能需要更多处理时间
3. **跟踪器选择**: 
   - ByteTrack: 速度优先
   - ByteVisionTrack: 精度优先

## 故障排除

### 常见问题

1. **模型权重文件不存在**
   ```
   解决方案: 检查配置文件中的权重路径，确保文件存在
   ```

2. **CUDA内存不足**
   ```
   解决方案: 减小输入视频分辨率或使用CPU模式
   ```

3. **视频格式不支持**
   ```
   解决方案: 转换为常见格式如MP4、AVI等
   ```

### 调试模式

启用详细日志输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 技术细节

### 跟踪器对比

| 特性 | ByteTrack | ByteVisionTrack |
|------|-----------|-----------------|
| 匹配方式 | IoU | IoU + 外观特征 |
| 计算复杂度 | 低 | 中等 |
| 遮挡处理 | 一般 | 优秀 |
| 相似目标 | 一般 | 优秀 |
| 实时性 | 优秀 | 良好 |

### 可视化特性

- 自动颜色分配：每个跟踪ID对应唯一颜色
- 智能标签位置：避免标签重叠
- 实时统计信息：显示当前帧跟踪数量
- 可配置显示选项：字体大小、线条粗细等

## 扩展开发

### 添加新的跟踪器

1. 在 `utils/tracker_utils.py` 中添加跟踪器类型判断
2. 在 `video_tracker_visualizer.py` 中添加初始化逻辑
3. 创建对应的配置文件

### 自定义可视化

修改 `utils/tracker_utils.py` 中的 `draw_tracking_results` 函数来自定义可视化效果。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目遵循原项目的许可证。
