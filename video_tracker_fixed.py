"""
基于现有MOTEvaluator的视频跟踪可视化程序
严格遵循代码规范，复用现有代码

作者: AI Assistant
创建时间: 2025-06-30
修复时间: 2025-06-30 10:45
"""

import os
import sys
import cv2
import torch
import numpy as np
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from detectron2.config import LazyConfig
from detectron2.utils.logger import setup_logger
from detectron2.structures import Instances, Boxes

# 复用现有的跟踪器评估器
from tracker.eval.evaluators import MOTEvaluator

# 复用现有的数据加载器
from datasets.builder import build_test_loader
from datasets.mot_mapper import MOTtestMapper
from datasets.data import ValTransform

# 复用现有的模型构建
from models.model_utils import get_model

logger = logging.getLogger("video_tracker_fixed")


class VideoDataset:
    """
    将视频转换为MOT格式的数据集
    复用现有的数据结构
    """

    def __init__(self, video_path: str):
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")

        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        logger.info(f"视频信息: {self.width}x{self.height}, {self.fps:.2f}fps, {self.total_frames}帧")

    def __len__(self):
        return self.total_frames

    def __getitem__(self, idx):
        """
        返回MOT格式的数据，与现有数据加载器兼容
        """
        # 设置帧位置
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = self.cap.read()

        if not ret:
            raise IndexError(f"无法读取第{idx}帧")

        # 构建与MOT数据集兼容的数据格式
        data_dict = {
            "file_name": f"video_frame_{idx:06d}.jpg",  # 虚拟文件名
            "height": self.height,
            "width": self.width,
            "image_id": idx,
            "frame_id": idx + 1,  # MOT格式从1开始
            "annotations": [],  # 空的标注，因为我们只做推理
            "ori_img": frame  # 原始图像
        }

        return data_dict

    def __del__(self):
        if hasattr(self, 'cap'):
            self.cap.release()


class VideoTracker:
    """
    基于现有MOTEvaluator的视频跟踪器
    完全复用现有代码逻辑
    """

    def __init__(self, config_path: str, model_weights: str = None):
        self.config_path = config_path
        self.model_weights = model_weights

        # 加载配置
        self.cfg = LazyConfig.load(config_path)

        # 初始化模型
        self._init_model()

        # 初始化数据预处理器
        self._init_preprocessor()

    def _init_model(self):
        """初始化模型 - 复用现有逻辑"""
        logger.info("初始化检测模型...")

        # 构建模型 - 与MOTEvaluator相同的方式
        model_kwargs = {}
        if isinstance(self.cfg.model, dict):
            model_kwargs = {k: v for k, v in self.cfg.model.items()}
        else:
            for k in dir(self.cfg.model):
                if not k.startswith('_'):
                    model_kwargs[k] = getattr(self.cfg.model, k)

        self.model = get_model(**model_kwargs)

        # 设置设备
        device = getattr(self.cfg.train, 'device', 'cuda' if torch.cuda.is_available() else 'cpu')
        self.device = torch.device(device)
        self.model.to(self.device)
        self.model.eval()

        # 加载权重
        if self.model_weights:
            checkpoint_path = self.model_weights
        elif hasattr(self.cfg.train, 'init_checkpoint'):
            checkpoint_path = self.cfg.train.init_checkpoint
        else:
            raise ValueError("未指定模型权重路径")

        if checkpoint_path and os.path.exists(checkpoint_path):
            logger.info(f"加载模型权重: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=False)

            if isinstance(checkpoint, dict):
                if 'model' in checkpoint:
                    state_dict = checkpoint['model']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint

            self.model.load_state_dict(state_dict, strict=False)
            logger.info("模型权重加载完成")

    def _init_preprocessor(self):
        """初始化预处理器 - 直接使用ValTransform"""
        from datasets.data import ValTransform

        test_size = self.cfg.dataloader.test.test_size

        self.preproc = ValTransform(
            rgb_means=(0.485, 0.456, 0.406),
            std=(0.229, 0.224, 0.225),
        )
        self.test_size = test_size

    def process_video(
        self,
        video_path: str,
        output_path: str = None,
        display: bool = True,
        save_video: bool = True
    ) -> List[List]:
        """
        处理视频 - 基于MOTEvaluator的逻辑
        """
        # 创建视频数据集
        video_dataset = VideoDataset(video_path)

        # 创建自定义数据加载器，直接处理视频帧
        class VideoDataLoader:
            def __init__(self, dataset, preproc, test_size):
                self.dataset = dataset
                self.preproc = preproc
                self.test_size = test_size

            def __iter__(self):
                for i in range(len(self.dataset)):
                    data = self.dataset[i]
                    ori_img = data["ori_img"]

                    # 直接应用预处理，不依赖文件路径
                    img, _ = self.preproc(ori_img, np.zeros((1, 5)), self.test_size)

                    # 构建与MOTEvaluator兼容的数据格式
                    processed_data = {
                        "image": torch.as_tensor(np.ascontiguousarray(img)),
                        "ori_img": ori_img,
                        "frame_id": data["frame_id"],
                        "file_name": data["file_name"],
                        "height": data["height"],
                        "width": data["width"]
                    }

                    yield [processed_data]  # MOTEvaluator期望batch格式

            def __len__(self):
                return len(self.dataset)

        # 创建数据加载器
        dataloader = VideoDataLoader(video_dataset, self.preproc, self.test_size)

        # 创建MOTEvaluator实例
        evaluator = MOTEvaluator(self.cfg, dataloader)

        # 准备输出视频写入器
        if save_video and output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, video_dataset.fps,
                                (video_dataset.width, video_dataset.height))

        # 存储跟踪结果
        all_tracks = []

        # 修改evaluator的evaluate方法来支持可视化
        # 这里我们需要重写部分逻辑
        tensor_type = torch.cuda.FloatTensor if self.device.type == 'cuda' else torch.FloatTensor

        # 获取跟踪器类型并初始化
        from utils.tracker_utils import determine_tracker_type
        from tracker.byte_tracker import BYTETracker
        from tracker.bytevision_tracker import BYTEVisionTracker

        tracker_type = determine_tracker_type(self.cfg.track)
        logger.info(f"使用跟踪器类型: {tracker_type}")

        if tracker_type == "ByteTracker":
            tracker = BYTETracker(self.cfg.track)
        elif tracker_type == "BYTEVisionTracker":
            tracker = BYTEVisionTracker(self.cfg.track)
        else:
            logger.warning(f"未知的跟踪器类型 '{tracker_type}'，使用默认的ByteTracker")
            tracker = BYTETracker(self.cfg.track)

        logger.info("开始处理视频...")

        for frame_idx, batch_data in enumerate(dataloader):
            with torch.no_grad():
                # 获取帧信息
                frame_id = batch_data[0]["frame_id"]
                ori_img = batch_data[0]["ori_img"]

                # 模型推理 - 与MOTEvaluator完全相同的方式
                batch_data[0]["image"] = batch_data[0]["image"].type(tensor_type)
                outputs = self.model(batch_data)

                # 获取检测结果
                det_instances = outputs[0]["instances"]

                # 跟踪更新 - 与MOTEvaluator完全相同的方式
                if det_instances is not None:
                    online_targets = tracker.update(det_instances, ori_img)
                else:
                    online_targets = []

                # 保存跟踪结果
                all_tracks.append(online_targets)

                # 可视化 - 复用现有的可视化逻辑
                vis_frame = self._visualize_frame(ori_img, online_targets, tracker_type)

                # 添加信息文本
                info_text = f"Frame: {frame_id}/{video_dataset.total_frames}, Tracks: {len(online_targets)}, Tracker: {tracker_type}"
                cv2.putText(vis_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                # 显示
                if display:
                    cv2.imshow('Video Tracking', vis_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break

                # 保存
                if save_video and output_path:
                    out.write(vis_frame)

                # 进度信息
                if frame_idx % 30 == 0:
                    logger.info(f"处理进度: {frame_idx+1}/{video_dataset.total_frames} ({(frame_idx+1)/video_dataset.total_frames*100:.1f}%)")

        # 清理资源
        if save_video and output_path:
            out.release()
        if display:
            cv2.destroyAllWindows()

        logger.info(f"视频处理完成，共处理 {len(all_tracks)} 帧")

        return all_tracks

    def _visualize_frame(self, image: np.ndarray, tracks: List, tracker_type: str) -> np.ndarray:
        """
        可视化单帧 - 复用现有的可视化逻辑
        基于tracker/eval/evaluators.py中的可视化代码
        """
        if len(tracks) == 0:
            return image.copy()

        # 复制图像
        vis_img = image.copy()

        # 准备颜色列表 (BGR格式) - 与现有代码相同
        colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (255, 255, 0), (0, 255, 255), (255, 0, 255)]

        # 绘制跟踪结果
        for i, track in enumerate(tracks):
            # 获取跟踪框信息 - 与现有代码相同的方式
            if tracker_type in ["Sort", "OCSort"]:
                x1, y1, x2, y2 = map(int, track[:4])
                track_id = int(track[4])
                score = track[5] if len(track) > 5 else 1.0
            else:
                # 标准格式 (ByteTracker, BYTEVisionTracker等)
                tlwh = track.tlwh
                x1, y1, w, h = map(int, tlwh)
                x2, y2 = x1 + w, y1 + h
                track_id = track.track_id
                score = track.score

            # 选择颜色
            color = colors[i % len(colors)]

            # 绘制边界框
            cv2.rectangle(vis_img, (x1, y1), (x2, y2), color, 2)

            # 添加文本标签 - 与现有代码相同的格式
            label = f"ID:{track_id} {score:.2f}"
            cv2.putText(vis_img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        return vis_img


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="基于现有代码的视频跟踪可视化程序")
    parser.add_argument("--input", "-i", required=True, help="输入视频路径")
    parser.add_argument("--output", "-o", help="输出视频路径")
    parser.add_argument("--tracker", "-t", choices=["bytetrack", "bytevision"],
                       default="bytetrack", help="跟踪器类型")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--weights", "-w", help="模型权重路径")
    parser.add_argument("--no-display", action="store_true", help="不显示实时画面")
    parser.add_argument("--no-save", action="store_true", help="不保存输出视频")

    args = parser.parse_args()

    # 设置日志
    setup_logger(name="video_tracker_fixed")

    # 确定配置文件
    if args.config:
        config_path = args.config
    else:
        if args.tracker == "bytetrack":
            config_path = "configs/video_track_bytetrack_cfg.py"
        elif args.tracker == "bytevision":
            config_path = "configs/video_track_bytevision_cfg.py"
        else:
            raise ValueError(f"不支持的跟踪器类型: {args.tracker}")

    # 确定输出路径
    if not args.output and not args.no_save:
        input_path = Path(args.input)
        args.output = str(input_path.parent / f"{input_path.stem}_{args.tracker}_fixed{input_path.suffix}")

    logger.info(f"输入视频: {args.input}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"配置文件: {config_path}")

    try:
        # 创建跟踪器
        tracker = VideoTracker(config_path, args.weights)

        # 处理视频
        tracks = tracker.process_video(
            args.input,
            args.output,
            display=not args.no_display,
            save_video=not args.no_save
        )

        logger.info("视频跟踪可视化完成！")

    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
