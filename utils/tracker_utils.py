"""
跟踪器工具函数模块
包含跟踪器类型确定、可视化等功能

作者: AI Assistant
创建时间: 2025-06-29
"""

import cv2
import numpy as np
import logging
from typing import List, Tuple, Optional, Union
import colorsys

logger = logging.getLogger("detectron2")


def determine_tracker_type(track_config) -> str:
    """
    根据配置文件中的布尔标志确定跟踪器类型
    
    Args:
        track_config: 跟踪配置对象，包含各种布尔标志
        
    Returns:
        str: 跟踪器类型名称
    """
    # 如果直接指定了tracker_type，优先使用
    if hasattr(track_config, 'tracker_type') and track_config.tracker_type:
        return track_config.tracker_type
    
    # 根据布尔标志确定跟踪器类型
    if hasattr(track_config, 'byte_vision') and track_config.byte_vision:
        return "BYTEVisionTracker"
    elif hasattr(track_config, 'sparse_vision') and track_config.sparse_vision:
        return "SparseVisionTracker"
    elif hasattr(track_config, 'byte') and track_config.byte:
        return "ByteTracker"
    elif hasattr(track_config, 'sort') and track_config.sort:
        return "Sort"
    elif hasattr(track_config, 'ocsort') and track_config.ocsort:
        return "OCSort"
    elif hasattr(track_config, 'bot') and track_config.bot:
        return "BotSort"
    else:
        # 默认使用ByteTracker
        logger.warning("未找到明确的跟踪器类型标志，使用默认的ByteTracker")
        return "ByteTracker"


def generate_colors(num_colors: int) -> List[Tuple[int, int, int]]:
    """
    生成指定数量的不同颜色
    
    Args:
        num_colors: 需要生成的颜色数量
        
    Returns:
        List[Tuple[int, int, int]]: BGR格式的颜色列表
    """
    colors = []
    for i in range(num_colors):
        hue = i / num_colors
        rgb = colorsys.hsv_to_rgb(hue, 1.0, 1.0)
        # 转换为BGR格式并缩放到0-255
        bgr = (int(rgb[2] * 255), int(rgb[1] * 255), int(rgb[0] * 255))
        colors.append(bgr)
    return colors


def draw_tracking_results(
    image: np.ndarray,
    tracks: List,
    tracker_type: str = "ByteTracker",
    show_confidence: bool = True,
    font_scale: float = 0.6,
    thickness: int = 2
) -> np.ndarray:
    """
    在图像上绘制跟踪结果
    
    Args:
        image: 输入图像 (BGR格式)
        tracks: 跟踪结果列表
        tracker_type: 跟踪器类型
        show_confidence: 是否显示置信度
        font_scale: 字体大小
        thickness: 线条粗细
        
    Returns:
        np.ndarray: 绘制了跟踪结果的图像
    """
    if len(tracks) == 0:
        return image
    
    # 复制图像以避免修改原图
    result_image = image.copy()
    
    # 生成足够的颜色
    max_id = max([get_track_id(track, tracker_type) for track in tracks])
    colors = generate_colors(max_id + 10)  # 多生成一些颜色
    
    for track in tracks:
        # 根据跟踪器类型获取信息
        if tracker_type in ["Sort", "OCSort"]:
            # Sort和OCSort返回的格式: [x1, y1, x2, y2, track_id, score]
            x1, y1, x2, y2 = map(int, track[:4])
            track_id = int(track[4])
            score = track[5] if len(track) > 5 else 1.0
        else:
            # 标准格式 (ByteTracker, BYTEVisionTracker等)
            tlwh = track.tlwh
            x1, y1, w, h = map(int, tlwh)
            x2, y2 = x1 + w, y1 + h
            track_id = track.track_id
            score = track.score
        
        # 选择颜色
        color = colors[track_id % len(colors)]
        
        # 绘制边界框
        cv2.rectangle(result_image, (x1, y1), (x2, y2), color, thickness)
        
        # 准备标签文本
        if show_confidence:
            label = f"ID:{track_id} {score:.2f}"
        else:
            label = f"ID:{track_id}"
        
        # 计算文本大小
        (text_width, text_height), baseline = cv2.getTextSize(
            label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness
        )
        
        # 绘制文本背景
        text_x, text_y = x1, y1 - 10
        if text_y < text_height:
            text_y = y1 + text_height + 10
        
        cv2.rectangle(
            result_image,
            (text_x, text_y - text_height - baseline),
            (text_x + text_width, text_y + baseline),
            color,
            -1
        )
        
        # 绘制文本
        cv2.putText(
            result_image,
            label,
            (text_x, text_y - baseline),
            cv2.FONT_HERSHEY_SIMPLEX,
            font_scale,
            (255, 255, 255),  # 白色文字
            thickness
        )
    
    return result_image


def get_track_id(track, tracker_type: str) -> int:
    """
    根据跟踪器类型获取track ID
    
    Args:
        track: 跟踪对象
        tracker_type: 跟踪器类型
        
    Returns:
        int: track ID
    """
    if tracker_type in ["Sort", "OCSort"]:
        return int(track[4])
    else:
        return track.track_id


def save_tracking_video(
    input_video_path: str,
    output_video_path: str,
    tracks_per_frame: List[List],
    tracker_type: str = "ByteTracker",
    fps: Optional[float] = None
) -> None:
    """
    保存带有跟踪结果的视频
    
    Args:
        input_video_path: 输入视频路径
        output_video_path: 输出视频路径
        tracks_per_frame: 每帧的跟踪结果
        tracker_type: 跟踪器类型
        fps: 输出视频帧率，如果为None则使用输入视频的帧率
    """
    # 打开输入视频
    cap = cv2.VideoCapture(input_video_path)
    if not cap.isOpened():
        raise ValueError(f"无法打开输入视频: {input_video_path}")
    
    # 获取视频属性
    input_fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    if fps is None:
        fps = input_fps
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
    
    frame_idx = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 如果有对应帧的跟踪结果，绘制它们
        if frame_idx < len(tracks_per_frame):
            frame = draw_tracking_results(frame, tracks_per_frame[frame_idx], tracker_type)
        
        # 写入帧
        out.write(frame)
        frame_idx += 1
    
    # 释放资源
    cap.release()
    out.release()
    
    logger.info(f"跟踪视频已保存到: {output_video_path}")


def print_tracking_stats(tracks_per_frame: List[List], tracker_type: str = "ByteTracker") -> None:
    """
    打印跟踪统计信息
    
    Args:
        tracks_per_frame: 每帧的跟踪结果
        tracker_type: 跟踪器类型
    """
    if not tracks_per_frame:
        logger.info("没有跟踪结果")
        return
    
    total_frames = len(tracks_per_frame)
    total_tracks = sum(len(tracks) for tracks in tracks_per_frame)
    avg_tracks_per_frame = total_tracks / total_frames if total_frames > 0 else 0
    
    # 统计唯一ID数量
    all_ids = set()
    for tracks in tracks_per_frame:
        for track in tracks:
            track_id = get_track_id(track, tracker_type)
            all_ids.add(track_id)
    
    logger.info(f"跟踪统计信息:")
    logger.info(f"  总帧数: {total_frames}")
    logger.info(f"  总跟踪数: {total_tracks}")
    logger.info(f"  平均每帧跟踪数: {avg_tracks_per_frame:.2f}")
    logger.info(f"  唯一ID数量: {len(all_ids)}")
    logger.info(f"  使用的跟踪器: {tracker_type}")
